# ChatAdvisor GitLab CI/CD 自动化部署

## 🚀 快速开始

### 一键设置

运行自动化设置脚本：

```bash
./ci/setup.sh
```

此脚本将自动完成：
- ✅ 生成SSH密钥
- ✅ 配置目标服务器连接
- ✅ 验证环境依赖
- ✅ 创建配置文件

### 手动配置GitLab变量

在GitLab项目的 **Settings > CI/CD > Variables** 中添加：

| 变量名 | 类型 | 值 |
|--------|------|-----|
| `SSH_PRIVATE_KEY` | File | SSH私钥内容 |
| `DEPLOY_HOST` | Variable | `**************` |
| `DEPLOY_USER` | Variable | `root` |
| `DEPLOY_PATH` | Variable | `/opt/chatadvisor` |

### 触发部署

提交信息包含 `release:` 关键字即可触发自动部署：

```bash
git commit -m "release: 更新前端界面"
git push origin main
```

## 📋 系统架构

```
Git提交(release:) → GitLab Runner(eva) → 构建 → 部署(**************) → 测试验证
```

### 服务端口

- **前端**: http://**************:34001
- **后端**: http://**************:53011

### 流水线阶段

1. **验证** - 检查环境和依赖
2. **构建** - 并行构建前端和后端
3. **部署** - SSH连接目标服务器部署
4. **测试** - 健康检查和Playwright自动化测试
5. **通知** - 部署结果通知

## 🔧 主要文件

```
.gitlab-ci.yml              # GitLab CI/CD主配置
ci/
├── scripts/
│   ├── deploy.sh           # 主部署脚本
│   ├── build-frontend.sh   # 前端构建
│   ├── build-backend.sh    # 后端构建
│   └── health-check.sh     # 健康检查
├── tests/
│   ├── playwright.config.js # 测试配置
│   └── tests/pipeline.spec.js # 自动化测试
└── setup.sh               # 一键设置脚本
```

## 🎭 自动化测试

### Playwright测试内容

- ✅ GitLab流水线页面访问
- ✅ 前端服务可访问性验证
- ✅ 后端API功能测试
- ✅ 前后端通信验证
- ✅ 性能和稳定性测试

### 手动运行测试

```bash
cd ci/tests
npm install
npx playwright install
npm test
```

## 📊 监控和日志

### GitLab流水线

访问地址：https://gitlab.zweiteng.tk/server/admin-frontend/-/pipelines

### 服务器日志

```bash
# 查看PM2服务状态
pm2 status

# 查看前端日志
pm2 logs admin-frontend-release

# 查看后端日志
pm2 logs chat-advisor-release
```

## 🔍 故障排除

### 常见问题

#### SSH连接失败
```bash
# 测试SSH连接
ssh -i ~/.ssh/chatadvisor_deploy root@**************

# 检查SSH服务
systemctl status ssh
```

#### 服务启动失败
```bash
# 检查PM2状态
pm2 status

# 查看错误日志
pm2 logs --err

# 手动启动调试
cd /opt/chatadvisor/ChatAdvisorServer
npm start
```

#### 构建失败
```bash
# 清理缓存
npm cache clean --force

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

### 调试模式

启用详细日志：
```bash
# 在GitLab CI/CD变量中设置
DEBUG=1
VERBOSE=1
```

## 📚 详细文档

完整文档请参考：[docs/CI_CD_DEPLOYMENT.md](docs/CI_CD_DEPLOYMENT.md)

包含内容：
- 🔧 详细配置说明
- 🏗️ 部署流程详解
- 🧪 测试用例说明
- 🛠️ 故障排除指南
- ⚡ 性能优化建议
- 🔒 安全配置指南

## 🆘 技术支持

遇到问题时：

1. 📋 查看GitLab流水线日志
2. 🔍 检查服务器日志
3. 📖 参考故障排除文档
4. 💬 联系技术支持团队

---

**版本**: v1.0  
**更新**: 2025-01-30  
**维护**: ChatAdvisor DevOps Team
