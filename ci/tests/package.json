{"name": "chatadvisor-ci-tests", "version": "1.0.0", "description": "ChatAdvisor CI/CD自动化测试", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:report": "playwright show-report", "install:browsers": "playwright install", "install:deps": "playwright install-deps"}, "keywords": ["playwright", "automation", "testing", "ci-cd", "gitlab"], "author": "ChatAdvisor Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0"}, "dependencies": {"axios": "^1.6.2"}}