// ChatAdvisor Playwright 测试配置
// 用于GitLab CI/CD流水线中的自动化测试

const { defineConfig, devices } = require('@playwright/test');

/**
 * @see https://playwright.dev/docs/test-configuration
 */
module.exports = defineConfig({
  // 测试目录
  testDir: './tests',
  
  // 全局测试超时时间（30秒）
  timeout: 30 * 1000,
  
  // 期望超时时间（5秒）
  expect: {
    timeout: 5000,
  },
  
  // 失败时重试次数
  retries: process.env.CI ? 2 : 0,
  
  // 并行执行的worker数量
  workers: process.env.CI ? 1 : undefined,
  
  // 报告器配置
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    ['list']
  ],
  
  // 全局设置
  use: {
    // 基础URL
    baseURL: process.env.BASE_URL || 'http://146.56.137.170:34001',
    
    // 浏览器上下文选项
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    
    // 导航超时
    navigationTimeout: 30 * 1000,
    
    // 操作超时
    actionTimeout: 10 * 1000,
  },

  // 项目配置 - 不同的浏览器和环境
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    
    // 移动端测试（可选）
    // {
    //   name: 'Mobile Chrome',
    //   use: { ...devices['Pixel 5'] },
    // },
    
    // 其他浏览器（可选）
    // {
    //   name: 'firefox',
    //   use: { ...devices['Desktop Firefox'] },
    // },
    // {
    //   name: 'webkit',
    //   use: { ...devices['Desktop Safari'] },
    // },
  ],

  // 输出目录
  outputDir: 'test-results/',
  
  // 全局设置和拆卸
  globalSetup: require.resolve('./global-setup.js'),
  globalTeardown: require.resolve('./global-teardown.js'),
  
  // Web服务器配置（如果需要启动本地服务器）
  // webServer: {
  //   command: 'npm start',
  //   port: 3000,
  //   reuseExistingServer: !process.env.CI,
  // },
});
