// ChatAdvisor Playwright 全局设置
// 在所有测试开始前执行的设置

const axios = require('axios');

async function globalSetup(config) {
  console.log('🚀 开始全局测试设置...');
  
  // 从环境变量获取配置
  const deployHost = process.env.DEPLOY_HOST || '**************';
  const frontendPort = process.env.FRONTEND_PORT || '34001';
  const backendPort = process.env.BACKEND_PORT || '53011';
  
  const frontendUrl = `http://${deployHost}:${frontendPort}`;
  const backendUrl = `http://${deployHost}:${backendPort}`;
  
  console.log(`前端URL: ${frontendUrl}`);
  console.log(`后端URL: ${backendUrl}`);
  
  // 等待服务启动
  console.log('⏳ 等待服务启动...');
  await waitForServices(frontendUrl, backendUrl);
  
  // 设置全局变量
  process.env.FRONTEND_URL = frontendUrl;
  process.env.BACKEND_URL = backendUrl;
  
  console.log('✅ 全局设置完成');
}

// 等待服务启动
async function waitForServices(frontendUrl, backendUrl, maxRetries = 30, delay = 2000) {
  console.log('🔍 检查服务可用性...');
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      // 检查前端服务
      const frontendResponse = await axios.get(frontendUrl, { 
        timeout: 5000,
        validateStatus: () => true 
      });
      
      // 检查后端服务
      const backendResponse = await axios.get(`${backendUrl}/health`, { 
        timeout: 5000,
        validateStatus: () => true 
      });
      
      if (frontendResponse.status === 200 && backendResponse.status === 200) {
        console.log('✅ 所有服务已就绪');
        return;
      }
      
      console.log(`⏳ 服务未就绪，重试 ${i + 1}/${maxRetries} (前端: ${frontendResponse.status}, 后端: ${backendResponse.status})`);
      
    } catch (error) {
      console.log(`⏳ 服务连接失败，重试 ${i + 1}/${maxRetries}: ${error.message}`);
    }
    
    // 等待后重试
    await new Promise(resolve => setTimeout(resolve, delay));
  }
  
  throw new Error('❌ 服务启动超时，无法连接到目标服务');
}

module.exports = globalSetup;
