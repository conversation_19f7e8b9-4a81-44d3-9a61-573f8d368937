// ChatAdvisor Playwright 全局清理
// 在所有测试结束后执行的清理工作

async function globalTeardown(config) {
  console.log('🧹 开始全局测试清理...');
  
  // 清理临时文件
  console.log('📁 清理临时文件...');
  
  // 生成测试摘要
  console.log('📊 生成测试摘要...');
  await generateTestSummary();
  
  console.log('✅ 全局清理完成');
}

// 生成测试摘要
async function generateTestSummary() {
  const fs = require('fs');
  const path = require('path');
  
  try {
    const resultsPath = path.join(__dirname, 'test-results', 'results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      const summary = {
        timestamp: new Date().toISOString(),
        total: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        skipped: results.stats?.skipped || 0,
        duration: results.stats?.duration || 0,
        environment: {
          frontendUrl: process.env.FRONTEND_URL,
          backendUrl: process.env.BACKEND_URL,
          deployHost: process.env.DEPLOY_HOST,
          ciCommit: process.env.CI_COMMIT_SHA,
          ciBranch: process.env.CI_COMMIT_REF_NAME
        }
      };
      
      // 写入摘要文件
      const summaryPath = path.join(__dirname, 'test-results', 'summary.json');
      fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
      
      console.log('📋 测试摘要:');
      console.log(`   总计: ${summary.total}`);
      console.log(`   通过: ${summary.passed}`);
      console.log(`   失败: ${summary.failed}`);
      console.log(`   跳过: ${summary.skipped}`);
      console.log(`   耗时: ${Math.round(summary.duration / 1000)}秒`);
      
    } else {
      console.log('⚠️ 未找到测试结果文件');
    }
    
  } catch (error) {
    console.log(`⚠️ 生成测试摘要失败: ${error.message}`);
  }
}

module.exports = globalTeardown;
