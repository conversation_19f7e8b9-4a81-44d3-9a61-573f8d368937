#!/bin/bash

# ChatAdvisor CI/CD 快速设置脚本
# 自动配置GitLab CI/CD环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo ""
    echo "🚀 ChatAdvisor CI/CD 快速设置"
    echo "================================"
    echo ""
    echo "此脚本将帮助您配置GitLab CI/CD自动化部署环境"
    echo ""
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查必需的命令
    local required_commands=("git" "ssh" "curl" "jq")
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            missing_deps+=("$cmd")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少必需的依赖: ${missing_deps[*]}"
        log_info "请安装缺少的依赖后重新运行此脚本"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 检查项目结构
check_project_structure() {
    log_info "检查项目结构..."
    
    local required_files=(
        ".gitlab-ci.yml"
        "admin-frontend/package.json"
        "ChatAdvisorServer/package.json"
        "ci/scripts/deploy.sh"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_error "缺少必需的文件: ${missing_files[*]}"
        log_info "请确保在项目根目录运行此脚本"
        exit 1
    fi
    
    log_success "项目结构检查完成"
}

# 生成SSH密钥
generate_ssh_key() {
    log_info "生成SSH密钥..."
    
    local ssh_dir="$HOME/.ssh"
    local key_name="chatadvisor_deploy"
    local private_key="$ssh_dir/${key_name}"
    local public_key="$ssh_dir/${key_name}.pub"
    
    # 创建SSH目录
    mkdir -p "$ssh_dir"
    chmod 700 "$ssh_dir"
    
    # 检查是否已存在密钥
    if [[ -f "$private_key" ]]; then
        log_warning "SSH密钥已存在: $private_key"
        read -p "是否重新生成？(y/n): " regenerate
        if [[ "$regenerate" != "y" && "$regenerate" != "Y" ]]; then
            log_info "使用现有SSH密钥"
            return 0
        fi
    fi
    
    # 生成新密钥
    log_info "生成新的SSH密钥..."
    ssh-keygen -t rsa -b 4096 -C "gitlab-ci@chatadvisor" -f "$private_key" -N ""
    
    log_success "SSH密钥生成完成"
    log_info "私钥位置: $private_key"
    log_info "公钥位置: $public_key"
    
    # 显示公钥内容
    echo ""
    log_info "请将以下公钥添加到目标服务器的 ~/.ssh/authorized_keys 文件中："
    echo ""
    cat "$public_key"
    echo ""
    
    read -p "按回车键继续..."
}

# 配置目标服务器
configure_target_server() {
    log_info "配置目标服务器..."
    
    # 获取服务器信息
    read -p "请输入目标服务器IP地址 [**************]: " deploy_host
    deploy_host=${deploy_host:-**************}
    
    read -p "请输入部署用户名 [root]: " deploy_user
    deploy_user=${deploy_user:-root}
    
    read -p "请输入部署路径 [/opt/chatadvisor]: " deploy_path
    deploy_path=${deploy_path:-/opt/chatadvisor}
    
    # 测试SSH连接
    log_info "测试SSH连接..."
    local ssh_key="$HOME/.ssh/chatadvisor_deploy"
    
    if ssh -i "$ssh_key" -o ConnectTimeout=10 -o StrictHostKeyChecking=no "$deploy_user@$deploy_host" "echo 'SSH连接测试成功'" > /dev/null 2>&1; then
        log_success "SSH连接测试成功"
    else
        log_error "SSH连接测试失败"
        log_info "请确保："
        log_info "1. 目标服务器SSH服务正常运行"
        log_info "2. 公钥已正确添加到目标服务器"
        log_info "3. 网络连接正常"
        exit 1
    fi
    
    # 检查目标服务器环境
    log_info "检查目标服务器环境..."
    ssh -i "$ssh_key" -o StrictHostKeyChecking=no "$deploy_user@$deploy_host" << 'EOF'
        echo "检查Node.js..."
        if ! command -v node &> /dev/null; then
            echo "错误: Node.js 未安装"
            exit 1
        fi
        echo "Node.js版本: $(node --version)"
        
        echo "检查npm..."
        if ! command -v npm &> /dev/null; then
            echo "错误: npm 未安装"
            exit 1
        fi
        echo "npm版本: $(npm --version)"
        
        echo "检查PM2..."
        if ! command -v pm2 &> /dev/null; then
            echo "错误: PM2 未安装"
            exit 1
        fi
        echo "PM2版本: $(pm2 --version)"
        
        echo "检查Git..."
        if ! command -v git &> /dev/null; then
            echo "错误: Git 未安装"
            exit 1
        fi
        echo "Git版本: $(git --version)"
        
        echo "✅ 服务器环境检查完成"
EOF
    
    if [[ $? -eq 0 ]]; then
        log_success "目标服务器环境检查通过"
    else
        log_error "目标服务器环境检查失败"
        exit 1
    fi
    
    # 保存配置
    cat > "ci/config.env" << EOF
# ChatAdvisor CI/CD 配置
DEPLOY_HOST=$deploy_host
DEPLOY_USER=$deploy_user
DEPLOY_PATH=$deploy_path
FRONTEND_PORT=34001
BACKEND_PORT=53011
EOF
    
    log_success "目标服务器配置完成"
}

# 设置GitLab CI/CD变量
setup_gitlab_variables() {
    log_info "设置GitLab CI/CD变量..."
    
    local ssh_key="$HOME/.ssh/chatadvisor_deploy"
    
    echo ""
    log_info "请在GitLab项目的CI/CD设置中添加以下变量："
    echo ""
    
    echo "1. SSH_PRIVATE_KEY (类型: File, 保护: 是)"
    echo "   内容:"
    echo "   ----------------------------------------"
    cat "$ssh_key"
    echo "   ----------------------------------------"
    echo ""
    
    echo "2. DEPLOY_HOST (类型: Variable)"
    echo "   值: $(grep DEPLOY_HOST ci/config.env | cut -d'=' -f2)"
    echo ""
    
    echo "3. DEPLOY_USER (类型: Variable)"
    echo "   值: $(grep DEPLOY_USER ci/config.env | cut -d'=' -f2)"
    echo ""
    
    echo "4. DEPLOY_PATH (类型: Variable)"
    echo "   值: $(grep DEPLOY_PATH ci/config.env | cut -d'=' -f2)"
    echo ""
    
    read -p "按回车键继续..."
}

# 测试CI/CD配置
test_cicd_config() {
    log_info "测试CI/CD配置..."
    
    # 检查.gitlab-ci.yml语法
    log_info "检查GitLab CI配置语法..."
    
    if command -v gitlab-ci-multi-runner &> /dev/null; then
        gitlab-ci-multi-runner exec shell validate_environment || log_warning "无法验证GitLab CI配置"
    else
        log_info "跳过GitLab CI语法检查（需要gitlab-runner）"
    fi
    
    # 测试脚本权限
    log_info "检查脚本权限..."
    local scripts=(
        "ci/scripts/deploy.sh"
        "ci/scripts/build-frontend.sh"
        "ci/scripts/build-backend.sh"
        "ci/scripts/health-check.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [[ ! -x "$script" ]]; then
            log_warning "脚本不可执行: $script"
            chmod +x "$script"
            log_info "已修复权限: $script"
        fi
    done
    
    log_success "CI/CD配置测试完成"
}

# 创建测试提交
create_test_commit() {
    log_info "创建测试提交..."
    
    read -p "是否创建测试提交来验证CI/CD流程？(y/n): " create_commit
    
    if [[ "$create_commit" == "y" || "$create_commit" == "Y" ]]; then
        # 检查Git状态
        if ! git status &> /dev/null; then
            log_error "当前目录不是Git仓库"
            return 1
        fi
        
        # 添加配置文件
        git add ci/config.env
        git add docs/CI_CD_DEPLOYMENT.md
        
        # 创建提交
        git commit -m "release: 配置CI/CD自动化部署系统

- 添加GitLab CI/CD流水线配置
- 配置自动化部署脚本
- 集成Playwright自动化测试
- 完善部署文档和故障排除指南"
        
        log_success "测试提交已创建"
        log_info "推送到远程仓库以触发CI/CD流程："
        log_info "git push origin main"
    else
        log_info "跳过测试提交创建"
    fi
}

# 显示完成信息
show_completion() {
    echo ""
    log_success "🎉 ChatAdvisor CI/CD 设置完成！"
    echo ""
    echo "配置摘要:"
    echo "  - GitLab CI/CD流水线: ✅ 已配置"
    echo "  - SSH密钥: ✅ 已生成"
    echo "  - 目标服务器: ✅ 已验证"
    echo "  - 部署脚本: ✅ 已就绪"
    echo "  - 自动化测试: ✅ 已配置"
    echo ""
    echo "下一步操作:"
    echo "1. 在GitLab项目中配置CI/CD变量"
    echo "2. 推送包含'release:'的提交来触发部署"
    echo "3. 访问GitLab流水线页面查看执行状态"
    echo ""
    echo "相关链接:"
    echo "  - 流水线页面: https://gitlab.zweiteng.tk/server/admin-frontend/-/pipelines"
    echo "  - 前端服务: http://$(grep DEPLOY_HOST ci/config.env | cut -d'=' -f2):34001"
    echo "  - 后端服务: http://$(grep DEPLOY_HOST ci/config.env | cut -d'=' -f2):53011"
    echo ""
    echo "文档参考:"
    echo "  - 详细文档: docs/CI_CD_DEPLOYMENT.md"
    echo "  - 故障排除: docs/CI_CD_DEPLOYMENT.md#故障排除"
    echo ""
}

# 主函数
main() {
    show_welcome
    check_dependencies
    check_project_structure
    generate_ssh_key
    configure_target_server
    setup_gitlab_variables
    test_cicd_config
    create_test_commit
    show_completion
}

# 错误处理
trap 'log_error "设置过程中发生错误，请检查日志"; exit 1' ERR

# 运行主函数
main "$@"
