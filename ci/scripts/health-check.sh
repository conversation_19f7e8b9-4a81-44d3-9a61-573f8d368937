#!/bin/bash

# ChatAdvisor 健康检查脚本
# 用于验证部署后的服务状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
DEPLOY_HOST="${DEPLOY_HOST:-**************}"
FRONTEND_PORT="${FRONTEND_PORT:-34001}"
BACKEND_PORT="${BACKEND_PORT:-53011}"
TIMEOUT="${TIMEOUT:-30}"
RETRY_COUNT="${RETRY_COUNT:-5}"
RETRY_DELAY="${RETRY_DELAY:-10}"

# 检查服务是否响应
check_service() {
    local service_name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    log_info "检查 $service_name 服务: $url"
    
    local retry=0
    while [[ $retry -lt $RETRY_COUNT ]]; do
        if curl -f -s -m "$TIMEOUT" "$url" > /dev/null 2>&1; then
            local status_code=$(curl -s -o /dev/null -w "%{http_code}" -m "$TIMEOUT" "$url")
            
            if [[ "$status_code" == "$expected_status" ]]; then
                log_success "$service_name 服务运行正常 (状态码: $status_code)"
                return 0
            else
                log_warning "$service_name 服务响应异常 (状态码: $status_code)"
            fi
        else
            log_warning "$service_name 服务无响应 (尝试 $((retry + 1))/$RETRY_COUNT)"
        fi
        
        retry=$((retry + 1))
        if [[ $retry -lt $RETRY_COUNT ]]; then
            log_info "等待 $RETRY_DELAY 秒后重试..."
            sleep "$RETRY_DELAY"
        fi
    done
    
    log_error "$service_name 服务健康检查失败"
    return 1
}

# 检查端口是否开放
check_port() {
    local host="$1"
    local port="$2"
    local service_name="$3"
    
    log_info "检查 $service_name 端口: $host:$port"
    
    if nc -z -w5 "$host" "$port" 2>/dev/null; then
        log_success "$service_name 端口 $port 开放"
        return 0
    else
        log_error "$service_name 端口 $port 无法访问"
        return 1
    fi
}

# 检查前端服务
check_frontend() {
    log_info "开始检查前端服务..."
    
    local frontend_url="http://$DEPLOY_HOST:$FRONTEND_PORT"
    
    # 检查端口
    if ! check_port "$DEPLOY_HOST" "$FRONTEND_PORT" "前端"; then
        return 1
    fi
    
    # 检查HTTP响应
    if ! check_service "前端" "$frontend_url" "200"; then
        return 1
    fi
    
    # 检查页面内容
    log_info "检查前端页面内容..."
    local content=$(curl -s -m "$TIMEOUT" "$frontend_url" 2>/dev/null || echo "")
    
    if [[ -n "$content" ]]; then
        if echo "$content" | grep -q "<!DOCTYPE html>" || echo "$content" | grep -q "<html"; then
            log_success "前端页面内容正常"
        else
            log_warning "前端页面内容可能异常"
        fi
    else
        log_warning "无法获取前端页面内容"
    fi
    
    log_success "前端服务健康检查完成"
    return 0
}

# 检查后端服务
check_backend() {
    log_info "开始检查后端服务..."
    
    local backend_url="http://$DEPLOY_HOST:$BACKEND_PORT"
    local health_url="$backend_url/health"
    
    # 检查端口
    if ! check_port "$DEPLOY_HOST" "$BACKEND_PORT" "后端"; then
        return 1
    fi
    
    # 检查健康检查端点
    if check_service "后端健康检查" "$health_url" "200"; then
        log_success "后端健康检查端点正常"
    else
        log_warning "后端健康检查端点异常，尝试检查根路径..."
        
        # 如果健康检查端点失败，尝试检查根路径
        if ! check_service "后端根路径" "$backend_url" "200"; then
            return 1
        fi
    fi
    
    # 检查API响应
    log_info "检查后端API响应..."
    local api_response=$(curl -s -m "$TIMEOUT" "$health_url" 2>/dev/null || echo "")
    
    if [[ -n "$api_response" ]]; then
        log_info "后端API响应: $api_response"
        
        # 检查是否是JSON格式
        if echo "$api_response" | jq . > /dev/null 2>&1; then
            log_success "后端API返回有效JSON"
        else
            log_info "后端API返回非JSON格式"
        fi
    else
        log_warning "无法获取后端API响应"
    fi
    
    log_success "后端服务健康检查完成"
    return 0
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    # 通过后端API检查数据库状态
    local db_check_url="http://$DEPLOY_HOST:$BACKEND_PORT/api/health/db"
    
    if curl -f -s -m "$TIMEOUT" "$db_check_url" > /dev/null 2>&1; then
        log_success "数据库连接正常"
    else
        log_warning "无法通过API检查数据库状态"
    fi
}

# 检查PM2进程状态
check_pm2_status() {
    log_info "检查PM2进程状态..."
    
    # 通过SSH检查PM2状态
    if [[ -n "$SSH_PRIVATE_KEY" ]]; then
        log_info "通过SSH检查远程PM2状态..."
        
        # 设置SSH
        mkdir -p ~/.ssh
        echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H "$DEPLOY_HOST" >> ~/.ssh/known_hosts 2>/dev/null || true
        
        # 检查PM2状态
        local pm2_status=$(ssh -o StrictHostKeyChecking=no "$DEPLOY_USER@$DEPLOY_HOST" "pm2 jlist" 2>/dev/null || echo "[]")
        
        if [[ "$pm2_status" != "[]" ]]; then
            log_success "PM2进程运行正常"
            
            # 解析PM2状态
            local frontend_status=$(echo "$pm2_status" | jq -r '.[] | select(.name=="admin-frontend-release") | .pm2_env.status' 2>/dev/null || echo "unknown")
            local backend_status=$(echo "$pm2_status" | jq -r '.[] | select(.name=="chat-advisor-release") | .pm2_env.status' 2>/dev/null || echo "unknown")
            
            log_info "前端进程状态: $frontend_status"
            log_info "后端进程状态: $backend_status"
        else
            log_warning "无法获取PM2进程状态"
        fi
        
        # 清理SSH密钥
        rm -f ~/.ssh/id_rsa
    else
        log_info "未配置SSH密钥，跳过PM2状态检查"
    fi
}

# 性能检查
check_performance() {
    log_info "执行性能检查..."
    
    # 检查前端响应时间
    log_info "检查前端响应时间..."
    local frontend_time=$(curl -o /dev/null -s -w "%{time_total}" -m "$TIMEOUT" "http://$DEPLOY_HOST:$FRONTEND_PORT" 2>/dev/null || echo "0")
    log_info "前端响应时间: ${frontend_time}秒"
    
    # 检查后端响应时间
    log_info "检查后端响应时间..."
    local backend_time=$(curl -o /dev/null -s -w "%{time_total}" -m "$TIMEOUT" "http://$DEPLOY_HOST:$BACKEND_PORT/health" 2>/dev/null || echo "0")
    log_info "后端响应时间: ${backend_time}秒"
    
    # 性能评估
    if (( $(echo "$frontend_time < 3.0" | bc -l) )); then
        log_success "前端响应时间良好"
    else
        log_warning "前端响应时间较慢: ${frontend_time}秒"
    fi
    
    if (( $(echo "$backend_time < 2.0" | bc -l) )); then
        log_success "后端响应时间良好"
    else
        log_warning "后端响应时间较慢: ${backend_time}秒"
    fi
}

# 生成健康检查报告
generate_health_report() {
    log_info "生成健康检查报告..."
    
    local report_file="health-check-report.txt"
    
    cat > "$report_file" << EOF
ChatAdvisor 健康检查报告
========================

检查时间: $(date)
目标主机: $DEPLOY_HOST
前端端口: $FRONTEND_PORT
后端端口: $BACKEND_PORT

服务状态:
- 前端服务: $(curl -s -o /dev/null -w "%{http_code}" -m 5 "http://$DEPLOY_HOST:$FRONTEND_PORT" 2>/dev/null || echo "无响应")
- 后端服务: $(curl -s -o /dev/null -w "%{http_code}" -m 5 "http://$DEPLOY_HOST:$BACKEND_PORT/health" 2>/dev/null || echo "无响应")

响应时间:
- 前端: $(curl -o /dev/null -s -w "%{time_total}" -m 10 "http://$DEPLOY_HOST:$FRONTEND_PORT" 2>/dev/null || echo "0")秒
- 后端: $(curl -o /dev/null -s -w "%{time_total}" -m 10 "http://$DEPLOY_HOST:$BACKEND_PORT/health" 2>/dev/null || echo "0")秒

检查状态: 完成 ✅
EOF
    
    log_info "健康检查报告已生成: $report_file"
}

# 主函数
main() {
    echo "🏥 ChatAdvisor 健康检查开始"
    echo "============================"
    echo ""
    
    local overall_status=0
    
    # 执行各项检查
    if ! check_frontend; then
        overall_status=1
    fi
    
    if ! check_backend; then
        overall_status=1
    fi
    
    check_database
    check_pm2_status
    check_performance
    generate_health_report
    
    echo ""
    if [[ $overall_status -eq 0 ]]; then
        log_success "🎉 所有服务健康检查通过！"
        echo ""
        echo "服务访问地址:"
        echo "  - 前端: http://$DEPLOY_HOST:$FRONTEND_PORT"
        echo "  - 后端: http://$DEPLOY_HOST:$BACKEND_PORT"
        echo ""
    else
        log_error "❌ 部分服务健康检查失败"
        echo ""
        echo "请检查服务状态和日志"
        exit 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
