#!/bin/bash

# ChatAdvisor 自动化部署脚本
# 用于GitLab CI/CD流水线中的远程部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
DEPLOY_HOST="${DEPLOY_HOST:-**************}"
DEPLOY_USER="${DEPLOY_USER:-root}"
DEPLOY_PATH="${DEPLOY_PATH:-/opt/chatadvisor}"
FRONTEND_PORT="${FRONTEND_PORT:-34001}"
BACKEND_PORT="${BACKEND_PORT:-53011}"

# SSH配置
SSH_OPTIONS="-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o LogLevel=ERROR"

# 检查必需的环境变量
check_environment() {
    log_info "检查部署环境变量..."
    
    local required_vars=("DEPLOY_HOST" "DEPLOY_USER" "SSH_PRIVATE_KEY")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "缺少必需的环境变量: ${missing_vars[*]}"
        exit 1
    fi
    
    log_success "环境变量检查完成"
}

# 设置SSH密钥
setup_ssh() {
    log_info "配置SSH连接..."
    
    mkdir -p ~/.ssh
    echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    chmod 600 ~/.ssh/id_rsa
    
    # 添加主机到known_hosts
    ssh-keyscan -H "$DEPLOY_HOST" >> ~/.ssh/known_hosts 2>/dev/null || true
    
    # 测试SSH连接
    if ssh $SSH_OPTIONS "$DEPLOY_USER@$DEPLOY_HOST" "echo 'SSH连接测试成功'" > /dev/null 2>&1; then
        log_success "SSH连接配置成功"
    else
        log_error "SSH连接测试失败"
        exit 1
    fi
}

# 清理SSH密钥
cleanup_ssh() {
    log_info "清理SSH配置..."
    rm -f ~/.ssh/id_rsa
}

# 检查目标服务器环境
check_remote_environment() {
    log_info "检查目标服务器环境..."
    
    ssh $SSH_OPTIONS "$DEPLOY_USER@$DEPLOY_HOST" << 'EOF'
        set -e
        
        # 检查Node.js
        if ! command -v node &> /dev/null; then
            echo "错误: Node.js 未安装"
            exit 1
        fi
        
        # 检查npm
        if ! command -v npm &> /dev/null; then
            echo "错误: npm 未安装"
            exit 1
        fi
        
        # 检查PM2
        if ! command -v pm2 &> /dev/null; then
            echo "错误: PM2 未安装"
            exit 1
        fi
        
        # 检查Git
        if ! command -v git &> /dev/null; then
            echo "错误: Git 未安装"
            exit 1
        fi
        
        echo "✅ 服务器环境检查完成"
        echo "Node.js版本: $(node --version)"
        echo "npm版本: $(npm --version)"
        echo "PM2版本: $(pm2 --version)"
EOF
    
    if [[ $? -eq 0 ]]; then
        log_success "目标服务器环境检查通过"
    else
        log_error "目标服务器环境检查失败"
        exit 1
    fi
}

# 备份当前部署
backup_current_deployment() {
    log_info "备份当前部署..."
    
    ssh $SSH_OPTIONS "$DEPLOY_USER@$DEPLOY_HOST" << EOF
        set -e
        
        if [[ -d "$DEPLOY_PATH" ]]; then
            BACKUP_DIR="$DEPLOY_PATH.backup.\$(date +%Y%m%d_%H%M%S)"
            echo "创建备份: \$BACKUP_DIR"
            cp -r "$DEPLOY_PATH" "\$BACKUP_DIR"
            
            # 只保留最近5个备份
            cd "\$(dirname "$DEPLOY_PATH")"
            ls -dt *.backup.* 2>/dev/null | tail -n +6 | xargs rm -rf
            
            echo "✅ 备份完成"
        else
            echo "⚠️ 目标目录不存在，跳过备份"
        fi
EOF
    
    log_success "备份操作完成"
}

# 更新代码
update_code() {
    log_info "更新代码仓库..."
    
    ssh $SSH_OPTIONS "$DEPLOY_USER@$DEPLOY_HOST" << EOF
        set -e
        
        # 确保目标目录存在
        mkdir -p "$DEPLOY_PATH"
        cd "$DEPLOY_PATH"
        
        # 如果是Git仓库，则拉取更新
        if [[ -d ".git" ]]; then
            echo "拉取最新代码..."
            git fetch origin
            git reset --hard origin/main
        else
            echo "⚠️ 不是Git仓库，请手动确保代码已更新"
        fi
        
        echo "✅ 代码更新完成"
EOF
    
    log_success "代码更新完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    ssh $SSH_OPTIONS "$DEPLOY_USER@$DEPLOY_HOST" << EOF
        set -e
        cd "$DEPLOY_PATH"
        
        # 安装后端依赖
        echo "安装后端依赖..."
        cd ChatAdvisorServer
        npm install --production --silent
        
        # 安装前端依赖
        echo "安装前端依赖..."
        cd ../admin-frontend
        npm install --silent
        
        echo "✅ 依赖安装完成"
EOF
    
    log_success "依赖安装完成"
}

# 构建项目
build_projects() {
    log_info "构建项目..."
    
    ssh $SSH_OPTIONS "$DEPLOY_USER@$DEPLOY_HOST" << EOF
        set -e
        cd "$DEPLOY_PATH"
        
        # 构建前端
        echo "构建前端项目..."
        cd admin-frontend
        npm run build:prod
        
        # 构建后端
        echo "构建后端项目..."
        cd ../ChatAdvisorServer
        npm run build
        
        echo "✅ 项目构建完成"
EOF
    
    log_success "项目构建完成"
}

# 停止现有服务
stop_services() {
    log_info "停止现有服务..."
    
    ssh $SSH_OPTIONS "$DEPLOY_USER@$DEPLOY_HOST" << EOF
        set -e
        
        # 停止前端服务
        pm2 stop admin-frontend-release 2>/dev/null || echo "前端服务未运行"
        
        # 停止后端服务
        pm2 stop chat-advisor-release 2>/dev/null || echo "后端服务未运行"
        
        echo "✅ 服务停止完成"
EOF
    
    log_success "服务停止完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    ssh $SSH_OPTIONS "$DEPLOY_USER@$DEPLOY_HOST" << EOF
        set -e
        cd "$DEPLOY_PATH/ChatAdvisorServer"
        
        # 启动前端服务
        echo "启动前端服务..."
        pm2 start pm2.config.js --only admin-frontend-release
        
        # 启动后端服务
        echo "启动后端服务..."
        pm2 start pm2.config.js --only chat-advisor-release
        
        # 保存PM2配置
        pm2 save
        
        echo "✅ 服务启动完成"
EOF
    
    log_success "服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待服务启动
    sleep 10
    
    # 检查前端服务
    log_info "检查前端服务 (端口 $FRONTEND_PORT)..."
    if curl -f -s "http://$DEPLOY_HOST:$FRONTEND_PORT" > /dev/null; then
        log_success "前端服务运行正常"
    else
        log_error "前端服务健康检查失败"
        return 1
    fi
    
    # 检查后端服务
    log_info "检查后端服务 (端口 $BACKEND_PORT)..."
    if curl -f -s "http://$DEPLOY_HOST:$BACKEND_PORT/health" > /dev/null; then
        log_success "后端服务运行正常"
    else
        log_error "后端服务健康检查失败"
        return 1
    fi
    
    log_success "所有服务健康检查通过"
}

# 显示部署信息
show_deployment_info() {
    log_success "🎉 部署完成！"
    echo ""
    echo "部署信息:"
    echo "  - 目标主机: $DEPLOY_HOST"
    echo "  - 部署路径: $DEPLOY_PATH"
    echo "  - 前端地址: http://$DEPLOY_HOST:$FRONTEND_PORT"
    echo "  - 后端地址: http://$DEPLOY_HOST:$BACKEND_PORT"
    echo "  - 提交哈希: ${CI_COMMIT_SHA:-未知}"
    echo ""
}

# 错误处理
handle_error() {
    log_error "部署过程中发生错误"
    cleanup_ssh
    exit 1
}

# 主函数
main() {
    echo "🚀 ChatAdvisor 自动化部署开始"
    echo "=================================="
    
    # 设置错误处理
    trap handle_error ERR
    
    # 执行部署步骤
    check_environment
    setup_ssh
    check_remote_environment
    backup_current_deployment
    update_code
    install_dependencies
    build_projects
    stop_services
    start_services
    health_check
    show_deployment_info
    
    # 清理
    cleanup_ssh
    
    log_success "部署流程完成"
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
