#!/bin/bash

# ChatAdvisor 前端构建脚本
# 专用于GitLab CI/CD流水线中的前端构建

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
FRONTEND_DIR="admin-frontend"
BUILD_MODE="${BUILD_MODE:-production}"
NODE_ENV="${NODE_ENV:-production}"

# 检查前端目录
check_frontend_directory() {
    log_info "检查前端项目目录..."
    
    if [[ ! -d "$FRONTEND_DIR" ]]; then
        log_error "前端目录不存在: $FRONTEND_DIR"
        exit 1
    fi
    
    if [[ ! -f "$FRONTEND_DIR/package.json" ]]; then
        log_error "前端项目配置文件不存在: $FRONTEND_DIR/package.json"
        exit 1
    fi
    
    log_success "前端目录检查完成"
}

# 检查Node.js环境
check_node_environment() {
    log_info "检查Node.js环境..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    local node_version=$(node --version)
    local npm_version=$(npm --version)
    
    log_info "Node.js版本: $node_version"
    log_info "npm版本: $npm_version"
    
    # 检查Node.js版本是否满足要求
    local required_version="20"
    local current_version=$(echo $node_version | sed 's/v//' | cut -d'.' -f1)
    
    if [[ $current_version -lt $required_version ]]; then
        log_warning "Node.js版本可能过低，建议使用v${required_version}+，当前版本: $node_version"
    fi
    
    log_success "Node.js环境检查完成"
}

# 清理构建缓存
clean_build_cache() {
    log_info "清理构建缓存..."
    
    cd "$FRONTEND_DIR"
    
    # 清理dist目录
    if [[ -d "dist" ]]; then
        rm -rf dist
        log_info "已清理dist目录"
    fi
    
    # 清理Vite缓存
    if [[ -d "node_modules/.vite" ]]; then
        rm -rf node_modules/.vite
        log_info "已清理Vite缓存"
    fi
    
    # 清理TypeScript缓存
    if [[ -f "tsconfig.tsbuildinfo" ]]; then
        rm -f tsconfig.tsbuildinfo
        log_info "已清理TypeScript缓存"
    fi
    
    cd ..
    log_success "构建缓存清理完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装前端依赖..."
    
    cd "$FRONTEND_DIR"
    
    # 检查package-lock.json是否存在
    if [[ -f "package-lock.json" ]]; then
        log_info "使用npm ci安装依赖..."
        npm ci --prefer-offline --no-audit
    else
        log_warning "package-lock.json不存在，使用npm install..."
        npm install --no-audit
    fi
    
    cd ..
    log_success "依赖安装完成"
}

# 代码质量检查
run_linting() {
    log_info "执行代码质量检查..."
    
    cd "$FRONTEND_DIR"
    
    # 检查是否有lint脚本
    if npm run | grep -q "lint"; then
        log_info "运行ESLint检查..."
        npm run lint
        log_success "代码质量检查通过"
    else
        log_warning "未找到lint脚本，跳过代码质量检查"
    fi
    
    cd ..
}

# TypeScript类型检查
run_type_check() {
    log_info "执行TypeScript类型检查..."
    
    cd "$FRONTEND_DIR"
    
    # 检查TypeScript配置
    if [[ -f "tsconfig.json" ]]; then
        log_info "运行TypeScript编译检查..."
        npx tsc --noEmit
        log_success "TypeScript类型检查通过"
    else
        log_warning "未找到tsconfig.json，跳过类型检查"
    fi
    
    cd ..
}

# 构建项目
build_project() {
    log_info "构建前端项目..."
    
    cd "$FRONTEND_DIR"
    
    # 设置环境变量
    export NODE_ENV="$NODE_ENV"
    
    # 选择构建命令
    local build_command
    if [[ "$BUILD_MODE" == "production" ]]; then
        if npm run | grep -q "build:prod"; then
            build_command="build:prod"
        else
            build_command="build"
        fi
    else
        build_command="build"
    fi
    
    log_info "使用构建命令: npm run $build_command"
    
    # 执行构建
    npm run "$build_command"
    
    cd ..
    log_success "项目构建完成"
}

# 验证构建结果
verify_build() {
    log_info "验证构建结果..."
    
    local dist_dir="$FRONTEND_DIR/dist"
    
    if [[ ! -d "$dist_dir" ]]; then
        log_error "构建目录不存在: $dist_dir"
        exit 1
    fi
    
    if [[ ! -f "$dist_dir/index.html" ]]; then
        log_error "主页文件不存在: $dist_dir/index.html"
        exit 1
    fi
    
    # 检查构建产物大小
    local build_size=$(du -sh "$dist_dir" | cut -f1)
    log_info "构建产物大小: $build_size"
    
    # 列出主要文件
    log_info "构建产物文件:"
    find "$dist_dir" -type f -name "*.html" -o -name "*.js" -o -name "*.css" | head -10 | while read file; do
        local file_size=$(du -h "$file" | cut -f1)
        echo "  - $(basename "$file"): $file_size"
    done
    
    log_success "构建结果验证通过"
}

# 生成构建报告
generate_build_report() {
    log_info "生成构建报告..."
    
    local report_file="frontend-build-report.txt"
    local dist_dir="$FRONTEND_DIR/dist"
    
    cat > "$report_file" << EOF
ChatAdvisor 前端构建报告
========================

构建时间: $(date)
构建模式: $BUILD_MODE
Node环境: $NODE_ENV
Node版本: $(node --version)
npm版本: $(npm --version)

构建产物信息:
- 构建目录: $dist_dir
- 总大小: $(du -sh "$dist_dir" | cut -f1)
- 文件数量: $(find "$dist_dir" -type f | wc -l)

主要文件:
$(find "$dist_dir" -type f -name "*.html" -o -name "*.js" -o -name "*.css" | head -20 | while read file; do
    echo "- $(basename "$file"): $(du -h "$file" | cut -f1)"
done)

构建状态: 成功 ✅
EOF
    
    log_info "构建报告已生成: $report_file"
}

# 错误处理
handle_error() {
    log_error "前端构建过程中发生错误"
    
    # 生成错误报告
    local error_report="frontend-build-error.txt"
    cat > "$error_report" << EOF
ChatAdvisor 前端构建错误报告
============================

构建时间: $(date)
构建模式: $BUILD_MODE
Node环境: $NODE_ENV
错误代码: $?

构建状态: 失败 ❌
EOF
    
    exit 1
}

# 显示构建信息
show_build_info() {
    echo ""
    log_success "🎉 前端构建完成！"
    echo ""
    echo "构建信息:"
    echo "  - 构建模式: $BUILD_MODE"
    echo "  - Node环境: $NODE_ENV"
    echo "  - 构建目录: $FRONTEND_DIR/dist"
    echo "  - 构建大小: $(du -sh "$FRONTEND_DIR/dist" | cut -f1)"
    echo ""
}

# 主函数
main() {
    echo "🏗️ ChatAdvisor 前端构建开始"
    echo "============================="
    
    # 设置错误处理
    trap handle_error ERR
    
    # 执行构建步骤
    check_frontend_directory
    check_node_environment
    clean_build_cache
    install_dependencies
    run_linting
    run_type_check
    build_project
    verify_build
    generate_build_report
    show_build_info
    
    log_success "前端构建流程完成"
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
