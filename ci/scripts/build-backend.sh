#!/bin/bash

# ChatAdvisor 后端构建脚本
# 专用于GitLab CI/CD流水线中的后端构建

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
BACKEND_DIR="ChatAdvisorServer"
BUILD_MODE="${BUILD_MODE:-production}"
NODE_ENV="${NODE_ENV:-production}"

# 检查后端目录
check_backend_directory() {
    log_info "检查后端项目目录..."
    
    if [[ ! -d "$BACKEND_DIR" ]]; then
        log_error "后端目录不存在: $BACKEND_DIR"
        exit 1
    fi
    
    if [[ ! -f "$BACKEND_DIR/package.json" ]]; then
        log_error "后端项目配置文件不存在: $BACKEND_DIR/package.json"
        exit 1
    fi
    
    if [[ ! -f "$BACKEND_DIR/tsconfig.json" ]]; then
        log_error "TypeScript配置文件不存在: $BACKEND_DIR/tsconfig.json"
        exit 1
    fi
    
    log_success "后端目录检查完成"
}

# 检查Node.js环境
check_node_environment() {
    log_info "检查Node.js环境..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    local node_version=$(node --version)
    local npm_version=$(npm --version)
    
    log_info "Node.js版本: $node_version"
    log_info "npm版本: $npm_version"
    
    # 检查Node.js版本是否满足要求
    local required_version="20"
    local current_version=$(echo $node_version | sed 's/v//' | cut -d'.' -f1)
    
    if [[ $current_version -lt $required_version ]]; then
        log_error "Node.js版本过低，要求v${required_version}+，当前版本: $node_version"
        exit 1
    fi
    
    log_success "Node.js环境检查完成"
}

# 清理构建缓存
clean_build_cache() {
    log_info "清理构建缓存..."
    
    cd "$BACKEND_DIR"
    
    # 清理dist目录
    if [[ -d "dist" ]]; then
        rm -rf dist
        log_info "已清理dist目录"
    fi
    
    # 清理TypeScript缓存
    if [[ -f "tsconfig.tsbuildinfo" ]]; then
        rm -f tsconfig.tsbuildinfo
        log_info "已清理TypeScript缓存"
    fi
    
    # 清理日志文件
    if [[ -d "logs" ]]; then
        find logs -name "*.log" -type f -delete 2>/dev/null || true
        log_info "已清理日志文件"
    fi
    
    cd ..
    log_success "构建缓存清理完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装后端依赖..."
    
    cd "$BACKEND_DIR"
    
    # 检查package-lock.json是否存在
    if [[ -f "package-lock.json" ]]; then
        log_info "使用npm ci安装依赖..."
        if [[ "$BUILD_MODE" == "production" ]]; then
            npm ci --only=production --prefer-offline --no-audit
        else
            npm ci --prefer-offline --no-audit
        fi
    else
        log_warning "package-lock.json不存在，使用npm install..."
        if [[ "$BUILD_MODE" == "production" ]]; then
            npm install --only=production --no-audit
        else
            npm install --no-audit
        fi
    fi
    
    cd ..
    log_success "依赖安装完成"
}

# 环境配置检查
check_environment_config() {
    log_info "检查环境配置..."
    
    cd "$BACKEND_DIR"
    
    # 检查.env文件
    if [[ -f ".env" ]]; then
        log_info "发现.env配置文件"
        
        # 检查关键配置项
        local required_vars=("OPENAI_API_KEY" "MONGODB_URI" "JWT_SECRET")
        local missing_vars=()
        
        for var in "${required_vars[@]}"; do
            if ! grep -q "^$var=" .env; then
                missing_vars+=("$var")
            fi
        done
        
        if [[ ${#missing_vars[@]} -gt 0 ]]; then
            log_warning "缺少环境变量: ${missing_vars[*]}"
        else
            log_success "环境配置检查通过"
        fi
    else
        log_warning "未找到.env配置文件"
    fi
    
    cd ..
}

# TypeScript编译
compile_typescript() {
    log_info "编译TypeScript代码..."
    
    cd "$BACKEND_DIR"
    
    # 检查TypeScript编译器
    if ! command -v tsc &> /dev/null; then
        log_info "安装TypeScript编译器..."
        npm install -g typescript
    fi
    
    # 执行编译
    log_info "开始TypeScript编译..."
    npm run build
    
    cd ..
    log_success "TypeScript编译完成"
}

# 验证构建结果
verify_build() {
    log_info "验证构建结果..."
    
    local dist_dir="$BACKEND_DIR/dist"
    
    if [[ ! -d "$dist_dir" ]]; then
        log_error "构建目录不存在: $dist_dir"
        exit 1
    fi
    
    # 检查主要入口文件
    local entry_files=("src/index.js" "src/app.js" "index.js" "app.js")
    local found_entry=false
    
    for entry in "${entry_files[@]}"; do
        if [[ -f "$dist_dir/$entry" ]]; then
            log_info "找到入口文件: $entry"
            found_entry=true
            break
        fi
    done
    
    if [[ "$found_entry" == false ]]; then
        log_warning "未找到标准入口文件，请检查构建配置"
    fi
    
    # 检查构建产物大小
    local build_size=$(du -sh "$dist_dir" | cut -f1)
    log_info "构建产物大小: $build_size"
    
    # 统计文件数量
    local js_files=$(find "$dist_dir" -name "*.js" | wc -l)
    local total_files=$(find "$dist_dir" -type f | wc -l)
    
    log_info "构建产物统计:"
    log_info "  - JavaScript文件: $js_files"
    log_info "  - 总文件数: $total_files"
    
    log_success "构建结果验证通过"
}

# 运行基础测试
run_basic_tests() {
    log_info "运行基础测试..."
    
    cd "$BACKEND_DIR"
    
    # 检查是否有测试脚本
    if npm run | grep -q "test"; then
        log_info "发现测试脚本，运行测试..."
        
        # 设置测试环境
        export NODE_ENV=test
        
        # 运行测试（允许失败，不中断构建）
        if npm test; then
            log_success "测试通过"
        else
            log_warning "测试失败，但不中断构建流程"
        fi
    else
        log_info "未找到测试脚本，跳过测试"
    fi
    
    cd ..
}

# 检查代码质量
check_code_quality() {
    log_info "检查代码质量..."
    
    cd "$BACKEND_DIR"
    
    # 检查是否有lint脚本
    if npm run | grep -q "lint"; then
        log_info "运行代码质量检查..."
        npm run lint || log_warning "代码质量检查发现问题"
    else
        log_info "未找到lint脚本，跳过代码质量检查"
    fi
    
    cd ..
}

# 生成构建报告
generate_build_report() {
    log_info "生成构建报告..."
    
    local report_file="backend-build-report.txt"
    local dist_dir="$BACKEND_DIR/dist"
    
    cat > "$report_file" << EOF
ChatAdvisor 后端构建报告
========================

构建时间: $(date)
构建模式: $BUILD_MODE
Node环境: $NODE_ENV
Node版本: $(node --version)
npm版本: $(npm --version)

构建产物信息:
- 构建目录: $dist_dir
- 总大小: $(du -sh "$dist_dir" | cut -f1)
- JavaScript文件: $(find "$dist_dir" -name "*.js" | wc -l)
- 总文件数: $(find "$dist_dir" -type f | wc -l)

主要文件:
$(find "$dist_dir" -name "*.js" | head -10 | while read file; do
    echo "- $(basename "$file"): $(du -h "$file" | cut -f1)"
done)

依赖信息:
- 生产依赖: $(cd "$BACKEND_DIR" && npm list --depth=0 --prod 2>/dev/null | grep -c "├──\|└──" || echo "未知")

构建状态: 成功 ✅
EOF
    
    log_info "构建报告已生成: $report_file"
}

# 错误处理
handle_error() {
    log_error "后端构建过程中发生错误"
    
    # 生成错误报告
    local error_report="backend-build-error.txt"
    cat > "$error_report" << EOF
ChatAdvisor 后端构建错误报告
============================

构建时间: $(date)
构建模式: $BUILD_MODE
Node环境: $NODE_ENV
错误代码: $?

构建状态: 失败 ❌
EOF
    
    exit 1
}

# 显示构建信息
show_build_info() {
    echo ""
    log_success "🎉 后端构建完成！"
    echo ""
    echo "构建信息:"
    echo "  - 构建模式: $BUILD_MODE"
    echo "  - Node环境: $NODE_ENV"
    echo "  - 构建目录: $BACKEND_DIR/dist"
    echo "  - 构建大小: $(du -sh "$BACKEND_DIR/dist" | cut -f1)"
    echo ""
}

# 主函数
main() {
    echo "🏗️ ChatAdvisor 后端构建开始"
    echo "============================="
    
    # 设置错误处理
    trap handle_error ERR
    
    # 执行构建步骤
    check_backend_directory
    check_node_environment
    clean_build_cache
    install_dependencies
    check_environment_config
    check_code_quality
    compile_typescript
    verify_build
    run_basic_tests
    generate_build_report
    show_build_info
    
    log_success "后端构建流程完成"
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
