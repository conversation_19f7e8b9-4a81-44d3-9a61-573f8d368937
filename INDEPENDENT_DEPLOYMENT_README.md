# ChatAdvisor 子项目独立部署

## 🚀 快速开始

### 独立部署架构

```
前端独立部署 ←→ 后端独立部署
     ↓                ↓
  PM2管理         PM2管理
     ↓                ↓
  34001端口       53011端口
```

### 一键部署命令

```bash
# 前端独立部署
./scripts/pm2-deploy.sh frontend deploy production

# 后端独立部署  
./scripts/pm2-deploy.sh backend deploy production

# 部署所有服务
./scripts/pm2-deploy.sh all deploy production
```

## 📋 子项目配置

### 前端项目 (admin-frontend)

**独立CI/CD配置**：
- 文件：`admin-frontend/.gitlab-ci.yml`
- 触发：提交信息包含 `release:`
- 端口：34001 (生产) / 54001 (开发)

**部署命令**：
```bash
cd admin-frontend
./scripts/deploy.sh --env production
```

### 后端项目 (ChatAdvisorServer)

**独立CI/CD配置**：
- 文件：`ChatAdvisorServer/.gitlab-ci.yml`
- 触发：提交信息包含 `release:`
- 端口：53011 (生产) / 33001 (开发)

**部署命令**：
```bash
cd ChatAdvisorServer
./scripts/deploy-backend.sh --env production
```

## 🔧 PM2便利管理

### 统一管理脚本

使用 `scripts/pm2-deploy.sh` 统一管理所有服务：

```bash
# 语法：./scripts/pm2-deploy.sh [组件] [操作] [环境]

# 组件：frontend, backend, all
# 操作：deploy, start, stop, restart, status, logs, build, health
# 环境：production, development
```

### 常用命令

```bash
# 🚀 部署服务
./scripts/pm2-deploy.sh frontend deploy production    # 部署前端
./scripts/pm2-deploy.sh backend deploy production     # 部署后端
./scripts/pm2-deploy.sh all deploy production         # 部署所有

# 📊 查看状态
./scripts/pm2-deploy.sh all status                    # 查看状态
./scripts/pm2-deploy.sh frontend logs production      # 查看日志
./scripts/pm2-deploy.sh all health production         # 健康检查

# 🔄 服务管理
./scripts/pm2-deploy.sh frontend restart production   # 重启前端
./scripts/pm2-deploy.sh backend stop production       # 停止后端
./scripts/pm2-deploy.sh all start production          # 启动所有

# 🏗️ 构建项目
./scripts/pm2-deploy.sh frontend build               # 构建前端
./scripts/pm2-deploy.sh backend build                # 构建后端
```

### 快捷命令

```bash
./scripts/pm2-deploy.sh --help      # 显示帮助
./scripts/pm2-deploy.sh --list      # 列出所有服务
./scripts/pm2-deploy.sh --version   # 显示版本
```

## 🔄 独立部署流程

### 前端独立发布

```bash
# 1. 进入前端目录
cd admin-frontend

# 2. 修改代码并提交
git add .
git commit -m "release: 更新用户界面"
git push origin main

# 3. 自动触发GitLab CI/CD
# 访问: https://gitlab.zweiteng.tk/server/admin-frontend/-/pipelines

# 4. 验证部署结果
curl http://**************:34001
```

### 后端独立发布

```bash
# 1. 进入后端目录
cd ChatAdvisorServer

# 2. 修改代码并提交
git add .
git commit -m "release: 更新API接口"
git push origin main

# 3. 自动触发GitLab CI/CD
# 访问: https://gitlab.zweiteng.tk/server/chatadvisor-server/-/pipelines

# 4. 验证部署结果
curl http://**************:53011/health
```

## 📊 服务监控

### PM2状态查看

```bash
# 查看所有服务
pm2 status

# 查看特定服务
pm2 show admin-frontend-release
pm2 show chat-advisor-release

# 实时监控
pm2 monit
```

### 服务访问地址

| 服务 | 生产环境 | 开发环境 |
|------|----------|----------|
| 前端 | http://**************:34001 | http://**************:54001 |
| 后端 | http://**************:53011 | http://**************:33001 |

### 日志查看

```bash
# PM2日志
pm2 logs admin-frontend-release  # 前端日志
pm2 logs chat-advisor-release    # 后端日志
pm2 logs                         # 所有日志

# 文件日志
tail -f admin-frontend/logs/admin-release-combined.log
tail -f ChatAdvisorServer/logs/release-combined.log
```

## 🛠️ 故障排除

### 快速诊断

```bash
# 1. 检查服务状态
./scripts/pm2-deploy.sh all status

# 2. 健康检查
./scripts/pm2-deploy.sh all health production

# 3. 查看日志
./scripts/pm2-deploy.sh all logs

# 4. 重启服务
./scripts/pm2-deploy.sh all restart production
```

### 常见问题

#### 前端无法访问

```bash
# 检查前端服务
pm2 status | grep admin-frontend
curl http://localhost:34001

# 重启前端
./scripts/pm2-deploy.sh frontend restart production
```

#### 后端API异常

```bash
# 检查后端服务
pm2 status | grep chat-advisor
curl http://localhost:53011/health

# 重启后端
./scripts/pm2-deploy.sh backend restart production
```

#### 构建失败

```bash
# 前端构建问题
cd admin-frontend
npm cache clean --force
rm -rf node_modules
npm install
npm run build:prod

# 后端构建问题
cd ChatAdvisorServer
npm cache clean --force
rm -rf node_modules dist
npm install
npm run build
```

## 🔧 GitLab CI/CD配置

### 必需变量

在GitLab项目的CI/CD设置中配置：

| 变量名 | 类型 | 值 |
|--------|------|-----|
| `SSH_PRIVATE_KEY` | File | SSH私钥内容 |
| `DEPLOY_HOST` | Variable | `**************` |
| `DEPLOY_USER` | Variable | `root` |
| `DEPLOY_PATH` | Variable | `/opt/chatadvisor` |

### 触发部署

提交信息包含 `release:` 关键字即可触发：

```bash
git commit -m "release: 版本更新"
git commit -m "feat: 新功能 release: 部署到生产"
git commit -m "release: v1.2.0 - 重要更新"
```

## 📚 详细文档

完整文档请参考：[docs/INDEPENDENT_DEPLOYMENT.md](docs/INDEPENDENT_DEPLOYMENT.md)

包含内容：
- 🏗️ 详细架构设计
- ⚙️ 完整配置说明
- 🔄 部署流程详解
- 🐛 故障排除指南
- 📈 监控和日志管理
- 🚀 最佳实践建议

## 💡 使用技巧

### 1. 环境切换

```bash
# 快速切换到开发环境
./scripts/pm2-deploy.sh all deploy development

# 切换回生产环境
./scripts/pm2-deploy.sh all deploy production
```

### 2. 部分更新

```bash
# 只更新前端
./scripts/pm2-deploy.sh frontend deploy production

# 只更新后端
./scripts/pm2-deploy.sh backend deploy production
```

### 3. 构建验证

```bash
# 先构建验证
./scripts/pm2-deploy.sh frontend build
./scripts/pm2-deploy.sh backend build

# 再部署
./scripts/pm2-deploy.sh all deploy production
```

### 4. 健康监控

```bash
# 定期健康检查
watch -n 30 './scripts/pm2-deploy.sh all health production'

# 设置定时任务
echo "*/5 * * * * /opt/chatadvisor/scripts/pm2-deploy.sh all health production" | crontab -
```

## 🆘 技术支持

遇到问题时：

1. 📋 查看PM2状态：`pm2 status`
2. 🔍 检查服务日志：`pm2 logs`
3. 🏥 执行健康检查：`./scripts/pm2-deploy.sh all health production`
4. 📖 参考详细文档：`docs/INDEPENDENT_DEPLOYMENT.md`
5. 💬 联系技术支持团队

---

**版本**: v1.0  
**更新**: 2025-01-30  
**维护**: ChatAdvisor DevOps Team
