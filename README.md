# VPN配置修复一键脚本

## 🚀 功能说明

这个脚本可以自动修复nginx和caddy的配置冲突，为VPN服务配置SSL证书，并生成客户端配置文件。

### ✨ 主要功能

- 🔧 自动检测sing-box VPN配置
- 🛑 停用冲突的caddy服务
- 📝 创建正确的nginx反向代理配置
- 🔒 自动获取Let's Encrypt SSL证书
- 🌐 配置WebSocket支持
- 📱 生成客户端配置文件（YAML和JSON格式）
- 💾 自动备份现有配置

## 📋 使用前提

1. 服务器已安装并配置sing-box
2. 已有VPN配置文件（以dava.cf结尾的.json文件）
3. 服务器已安装nginx
4. 域名已正确解析到服务器IP

## 🚀 使用方法

### 1. 下载脚本

```bash
wget https://raw.githubusercontent.com/your-repo/vpn_fix_script.sh
# 或者直接复制脚本内容到服务器
```

### 2. 添加执行权限

```bash
chmod +x vpn_fix_script.sh
```

### 3. 运行脚本

```bash
sudo ./vpn_fix_script.sh
```

## 📖 脚本执行流程

1. **检查权限**: 确保以root权限运行
2. **备份配置**: 备份现有nginx和caddy配置
3. **检测VPN配置**: 自动发现sing-box配置文件
4. **解析配置**: 提取域名、UUID、路径等信息
5. **停用caddy**: 停止并禁用caddy服务
6. **创建nginx配置**: 为每个VPN域名创建反向代理配置
7. **获取SSL证书**: 使用certbot自动获取Let's Encrypt证书
8. **修正WebSocket配置**: 添加WebSocket升级检查
9. **生成客户端配置**: 输出YAML和JSON格式的客户端配置

## 📁 输出文件

### 配置文件位置
- **YAML格式**: `/tmp/vpn_client_config.yaml`
- **JSON格式**: `/tmp/vpn_client_config.json`

### 备份文件位置
- **备份目录**: `/tmp/vpn_backup_YYYYMMDD_HHMMSS/`

## 🎯 客户端配置示例

生成的配置文件包含以下内容：

```yaml
proxies:
  - {
    name: 🚀 v2ray1.dava.cf,
    server: v2ray1.dava.cf,
    port: 443,
    type: vmess,
    uuid: 7e2fc85c-856c-4fbf-b0cf-441b1ce23a85,
    alterId: 0,
    cipher: auto,
    tls: true,
    network: ws,
    ws-opts: {
      path: /7e2fc85c-856c-4fbf-b0cf-441b1ce23a85,
      headers: {
        Host: v2ray1.dava.cf
      }
    },
    sni: v2ray1.dava.cf
  }

proxy-groups:
  - name: 🚀 节点选择
    type: select
    proxies:
      - 🚀 v2ray1.dava.cf
      - DIRECT
```

## 🔧 故障排除

### 常见问题

1. **权限错误**
   ```bash
   # 确保以root权限运行
   sudo ./vpn_fix_script.sh
   ```

2. **未找到VPN配置**
   ```bash
   # 检查sing-box配置文件是否存在
   find /etc -name "*dava.cf*.json"
   ```

3. **SSL证书获取失败**
   - 检查域名解析是否正确
   - 确保80端口未被其他服务占用
   - 检查防火墙设置

4. **nginx配置错误**
   ```bash
   # 检查nginx配置语法
   sudo nginx -t
   
   # 查看nginx错误日志
   sudo tail -f /var/log/nginx/error.log
   ```

### 手动检查

```bash
# 检查服务状态
sudo systemctl status nginx
sudo systemctl status caddy

# 检查端口占用
sudo netstat -tlnp | grep ':80\|:443'

# 检查sing-box监听端口
sudo netstat -tlnp | grep sing-box
```

## 🛡️ 安全说明

1. 脚本会自动备份配置文件
2. 所有操作都有详细日志输出
3. 如果配置失败，可以从备份恢复
4. SSL证书使用Let's Encrypt，安全可靠

## 📞 支持的客户端

生成的配置文件支持以下客户端：

- **Clash**: 使用YAML格式配置
- **ClashX**: macOS客户端
- **Clash for Android**: Android客户端
- **Clash for Windows**: Windows客户端
- **V2Ray客户端**: 可以手动转换配置

## 🔄 更新和维护

### 定期检查

```bash
# 检查证书有效期
sudo certbot certificates

# 重新生成客户端配置
sudo ./vpn_fix_script.sh
```

### 证书自动续期

Let's Encrypt证书会自动续期，无需手动干预。

## ⚠️ 注意事项

1. 运行前请确保已备份重要配置
2. 脚本会停用caddy服务，如果您需要caddy请手动调整
3. 确保域名已正确解析到服务器IP
4. 建议在测试环境先验证脚本功能

## 📝 日志说明

脚本运行时会输出彩色日志：
- 🔵 **[INFO]**: 一般信息
- 🟢 **[SUCCESS]**: 操作成功  
- 🟡 **[WARNING]**: 警告信息
- 🔴 **[ERROR]**: 错误信息

## 🆘 获取帮助

如果遇到问题，请：

1. 查看脚本输出的错误信息
2. 检查备份文件是否完整
3. 查看系统日志：`journalctl -xe`
4. 提供详细的错误信息以获得支持 