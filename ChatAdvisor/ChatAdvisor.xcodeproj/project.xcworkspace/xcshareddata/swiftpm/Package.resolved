{"originHash": "a2741326a6d5123cab7076fa0d7ea7b4d605f98edef935bc0fcdee615b3427e1", "pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "bbe8b69694d7873315fd3a4ad41efe043e1c07c5", "version": "1.2024072200.0"}}, {"identity": "alamofire", "kind": "remoteSourceControl", "location": "https://github.com/Alamofire/Alamofire.git", "state": {"revision": "513364f870f6bfc468f9d2ff0a95caccc10044c5", "version": "5.10.2"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "61b85103a1aeed8218f17c794687781505fbbef5", "version": "11.2.0"}}, {"identity": "appauth-ios", "kind": "remoteSourceControl", "location": "https://github.com/openid/AppAuth-iOS.git", "state": {"revision": "2781038865a80e2c425a1da12cc1327bcd56501f", "version": "1.7.6"}}, {"identity": "eventsource", "kind": "remoteSourceControl", "location": "https://github.com/Recouse/EventSource.git", "state": {"revision": "d783b1cf60599dbcec6396c55a6bab33a1c92dc3", "version": "0.1.4"}}, {"identity": "facebook-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/facebook/facebook-ios-sdk", "state": {"revision": "3fe31c168903759de1c5752d12856c5c437c6862", "version": "16.3.1"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk.git", "state": {"revision": "fdc352fabaf5916e7faa1f96ad02b1957e93e5a5", "version": "11.15.0"}}, {"identity": "google-ads-on-device-conversion-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/googleads/google-ads-on-device-conversion-ios-sdk", "state": {"revision": "428d8bb138e00f9a3f4f61cc6cd8863607524f65", "version": "2.1.0"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "45ce435e9406d3c674dd249a042b932bee006f60", "version": "11.15.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "617af071af9aa1d6a091d59a202910ac482128f9", "version": "10.1.0"}}, {"identity": "googlesignin-ios", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleSignIn-iOS", "state": {"revision": "65fb3f1aa6ffbfdc79c4e22178a55cd91561f5e9", "version": "8.0.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "60da361632d0de02786f709bdc0c4df340f7613e", "version": "8.1.0"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "cc0001a0cf963aa40501d9c2b181e7fc9fd8ec71", "version": "1.69.0"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "a2ab612cb980066ee56d90d60d8462992c07f24b", "version": "3.5.0"}}, {"identity": "gtmappauth", "kind": "remoteSourceControl", "location": "https://github.com/google/GTMAppAuth.git", "state": {"revision": "5d7d66f647400952b1758b230e019b07c0b4b22a", "version": "4.1.1"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "040d087ac2267d2ddd4cca36c757d1c6a05fdbfe", "version": "101.0.0"}}, {"identity": "keychain-swift", "kind": "remoteSourceControl", "location": "https://github.com/evgenyneu/keychain-swift.git", "state": {"revision": "75677d8be3c50898c8ffd0d107f88aa2740301fc", "version": "22.0.0"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "localize-swift", "kind": "remoteSourceControl", "location": "https://github.com/marmelroy/Localize-Swift.git", "state": {"revision": "0aa221fed0512772eaad04171d966b8d77b15212", "version": "3.2.0"}}, {"identity": "lookinserver", "kind": "remoteSourceControl", "location": "https://github.com/QMUI/LookinServer/", "state": {"revision": "e553d1b689d147817dc54ad5c28fcff71e860101", "version": "1.2.8"}}, {"identity": "moya", "kind": "remoteSourceControl", "location": "https://github.com/Moya/Moya.git", "state": {"revision": "c263811c1f3dbf002be9bd83107f7cdc38992b26", "version": "15.0.3"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "networkimage", "kind": "remoteSourceControl", "location": "https://github.com/gonzalezreal/NetworkImage", "state": {"revision": "2849f5323265386e200484b0d0f896e73c3411b9", "version": "6.0.1"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "reactiveswift", "kind": "remoteSourceControl", "location": "https://github.com/ReactiveCocoa/ReactiveSwift.git", "state": {"revision": "c43bae3dac73fdd3cb906bd5a1914686ca71ed3c", "version": "6.7.0"}}, {"identity": "rxswift", "kind": "remoteSourceControl", "location": "https://github.com/ReactiveX/RxSwift.git", "state": {"revision": "5dd1907d64f0d36f158f61a466bab75067224893", "version": "6.9.0"}}, {"identity": "simpletoast", "kind": "remoteSourceControl", "location": "https://github.com/sanzaru/SimpleToast.git", "state": {"revision": "8d49c8c16d218b03c3428a0d1c8fe79f4e2707a6", "version": "0.11.0"}}, {"identity": "splash", "kind": "remoteSourceControl", "location": "https://github.com/JohnSundell/Splash", "state": {"revision": "7f4df436eb78fe64fe2c32c58006e9949fa28ad8", "version": "0.16.0"}}, {"identity": "sqlcipher", "kind": "remoteSourceControl", "location": "https://github.com/Tencent/sqlcipher", "state": {"revision": "5d8825c22feedb421ad6f9ecfc1399460e10d299", "version": "1.4.7"}}, {"identity": "stepperview", "kind": "remoteSourceControl", "location": "https://github.com/badrinathvm/StepperView.git", "state": {"revision": "c66fb84c640fbc312f4eead0b7ae2a77f93e0839", "version": "1.6.7"}}, {"identity": "swift-cmark", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-cmark", "state": {"revision": "b022b08312decdc46585e0b3440d97f6f22ef703", "version": "0.6.0"}}, {"identity": "swift-eventsource", "kind": "remoteSourceControl", "location": "https://github.com/LaunchDarkly/swift-eventsource.git", "state": {"revision": "57051701c58a93603ffa2051f8e9cf0c8cff7814", "version": "3.3.0"}}, {"identity": "swift-markdown-ui", "kind": "remoteSourceControl", "location": "https://github.com/gonzalezreal/swift-markdown-ui", "state": {"revision": "5f613358148239d0292c0cef674a3c2314737f9e", "version": "2.4.1"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "102a647b573f60f73afdce5613a51d71349fe507", "version": "1.30.0"}}, {"identity": "swifterswift", "kind": "remoteSourceControl", "location": "https://github.com/SwifterSwift/SwifterSwift.git", "state": {"revision": "39fa28c90a3ebe3d53f80289304fd880cf2c42d0", "version": "6.2.0"}}, {"identity": "tiktok-opensdk-ios", "kind": "remoteSourceControl", "location": "https://github.com/tiktok/tiktok-opensdk-ios.git", "state": {"branch": "main", "revision": "cb0cdc785b653c3be116fd1302ec280fbcfac695"}}, {"identity": "wcdb", "kind": "remoteSourceControl", "location": "https://github.com/Tencent/wcdb.git", "state": {"revision": "f54d02ea07294d142b0fae9a4c942343b9e9c8b3", "version": "2.1.10"}}], "version": 3}