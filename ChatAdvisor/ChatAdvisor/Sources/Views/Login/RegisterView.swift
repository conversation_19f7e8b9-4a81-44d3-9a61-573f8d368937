//
//  RegisterView.swift
//
//
//  Created by zwt on 2024/4/11.
//

import Foundation
import SimpleToast
import SwifterSwift
import SwiftUI

struct RegisterView: View {
    @Binding var isLoggedIn: Bool
    @FocusState var isEmailFocused: Bool
    @FocusState var isPasswordFocused: Bool
    @StateObject var viewModel = RegisterViewModel()

    // Timer properties
    @State private var secondsLeft = 60
    let timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    @State private var timerIsRunning = false

    var body: some View {
        NavigationView {
            VStack {
                Text("请输入你的邮箱地址，以及大于6位数的密码".localized())
                    .foregroundColor(.mainDark)

                VStack(spacing: 20) {
                    InputField(placeholder: "邮箱".localized(),
                               text: $viewModel.email,
                               isValid: viewModel.isEmailValid,
                               keyboardType: .emailAddress,
                               textContentType: .emailAddress)
                        .disabled(viewModel.isEditingDisabled)
                        .focused($isEmailFocused)
                        .onChange(of: viewModel.email) { _ in
                            if isEmailFocused, viewModel.email.isValidEmail {
                                viewModel.isEmailValid = true
                            }
                            viewModel.sendButtonDisable = !(viewModel.email.isValidEmail && viewModel.password.count >= 6 && viewModel.isPasswordValid)
                        }
                        .onChange(of: isEmailFocused) { isFocused in
                            if !isFocused {
                                viewModel.isEmailValid = viewModel.email.isValidEmail
                            }
                        }

                    InputField(placeholder: "密码".localized(),
                               text: $viewModel.password,
                               isSecure: true,
                               isValid: viewModel.isPasswordValid,
                               textContentType: .password)
                        .disabled(viewModel.isEditingDisabled)
                        .focused($isPasswordFocused)
                        .onChange(of: viewModel.password) { _ in
                            if viewModel.password.count >= 6 {
                                viewModel.isPasswordValid = true
                            } else {
                                viewModel.isPasswordValid = false
                            }
                            viewModel.sendButtonDisable = !(viewModel.isEmailValid && viewModel.isPasswordValid)
                        }
                        .onChange(of: isPasswordFocused) { isFocused in
                            if !isFocused {
                                viewModel.isPasswordValid = viewModel.password.count >= 6
                            }
                        }

                    Button(action: {
                        viewModel.sendVerificationCode()
                    }) {
                        HStack {
                            if viewModel.isLoading {
                                ProgressView()
                                    .tint(.white)
                            } else {
                                Text(viewModel.isTimerActive ? "\(secondsLeft) \("秒后重发".localized())" : "发送".localized())
                            }
                        }
                        .frame(width: AppThemes.buttonMaxWidth, height: AppThemes.buttonHeight)
                        .background(viewModel.sendButtonDisable ? Color.mainBackground.opacity(0.6) : .blue)
                        .foregroundColor(.white)
                        .font(.system(size: AppThemes.fontSize, weight: .medium))
                        .cornerRadius(AppThemes.cornerRadius)
                        .animation(.easeInOut(duration: 0.2), value: viewModel.sendButtonDisable)
                    }
                    .disabled(viewModel.sendButtonDisable)
                    .padding(.horizontal, 20)

                    if viewModel.showingVerificationButton == true {
                        Button(action: {
                            viewModel.showingVerificationView = true
                        }) {
                            HStack {
                                Text("验证".localized())
                            }
                        }
                        .padding(.horizontal, 20)
                    }
                }
                .padding(.horizontal, 20)
                .transition(.opacity)
                .animation(.easeInOut(duration: 0.2), value: viewModel.showToast)
                .background(
                    NavigationLink(
                        destination: VerificationView(isLoggedIn: $isLoggedIn, viewModel: VerificationViewModel(email: viewModel.email, password: viewModel.password)),
                        isActive: $viewModel.showingVerificationView
                    ) {
                        EmptyView()
                    }
                    .hidden() // 隐藏导航链接
                )
            }
            .onAppear {
                FirebaseManager.shared.logPageView(pageName: "注册")
            }
            .navigationBarTitle("注册".localized(), displayMode: .inline)
            .padding(AppThemes.padding / 2)
            .contentShape(Rectangle())
            .simpleToast(isPresented: $viewModel.showToast, options: AppThemes.toastOptions) {
                Label(viewModel.toastMessage, systemImage: "exclamationmark.triangle")
                    .padding()
                    .background(viewModel.isRequestError ? Color.red.opacity(0.8) : Color.mainBackground)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                    .padding(.top)
            }
            .onReceive(timer) { _ in
                if viewModel.isTimerActive, timerIsRunning {
                    if secondsLeft > 0 {
                        viewModel.sendButtonDisable = true
                        secondsLeft -= 1
                    } else {
                        viewModel.sendButtonDisable = false
                        viewModel.isTimerActive = false
                        secondsLeft = 60
                    }
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.willResignActiveNotification)) { _ in
                // 应用即将进入后台时手动取消导航链接的激活状态
                viewModel.showingVerificationView = false
            }
            .onAppear {
                isEmailFocused = true
                startTimer()
            }
            .onDisappear {
                stopTimer()
            }
        }
    }

    // 启动计时器
    private func startTimer() {
        if !timerIsRunning {
            timerIsRunning = true
        }
    }

    // 停止计时器
    private func stopTimer() {
        timerIsRunning = false
    }
}
