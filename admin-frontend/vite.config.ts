import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// 读取package.json获取版本号
const packageJson = require('./package.json')

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 根据环境确定端口和API目标
  const isDev = command === 'serve'
  const port = parseInt(process.env.PORT || '3000')

  // 根据前端端口确定后端API端口
  let apiTarget = 'http://localhost:33001' // 默认debug后端
  if (port === 34001) {
    apiTarget = 'http://localhost:53011' // release后端
  } else if (port === 54001) {
    apiTarget = 'http://localhost:33001' // debug后端
  }

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    define: {
      // 将版本号注入到环境变量中
      'process.env.REACT_APP_VERSION': JSON.stringify(packageJson.version),
    },
    server: {
      port,
      host: true, // 允许外部访问
      proxy: {
        '/api': {
          target: apiTarget,
          changeOrigin: true,
        },
      },
      // 确保SPA路由正常工作
      historyApiFallback: true,
    },
    preview: {
      port,
      host: true, // 允许外部访问
      proxy: {
        '/api': {
          target: apiTarget,
          changeOrigin: true,
        },
      },
    },
    build: {
      outDir: 'dist',
      sourcemap: true,
    },
  }
})
