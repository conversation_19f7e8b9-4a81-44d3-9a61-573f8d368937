# ChatAdvisor Admin Frontend GitLab CI/CD 配置
# 当提交信息包含 "release:" 时自动触发前端部署
# 使用本地PM2部署，无需SSH连接

variables:
  FRONTEND_PORT: "34001"
  SERVICE_NAME: "admin-frontend-release"
  NODE_VERSION: "20"
  NPM_CACHE_DIR: ".npm"

cache:
  key: frontend-${CI_COMMIT_REF_SLUG}
  paths:
    - .npm/
    - node_modules/

stages:
  - validate
  - build
  - deploy
  - test
  - notify

workflow:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /release:/
      when: always
    - if: $CI_PIPELINE_SOURCE == "web"
      when: always
    - when: never

validate_frontend:
  stage: validate
  tags:
    - Eva
  script:
    - "echo '🔍 验证前端部署环境...'"
    - "echo '提交信息: $CI_COMMIT_MESSAGE'"
    - "echo '分支: $CI_COMMIT_REF_NAME'"
    - "echo '项目: Admin Frontend'"
    - "node --version"
    - "npm --version"
    - "echo '✅ 前端环境验证完成'"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

build_frontend:
  stage: build
  tags:
    - Eva
  script:
    - "echo '🏗️ 构建前端项目...'"
    - "npm ci --cache .npm --prefer-offline"
    - "echo '🔍 代码质量检查...'"
    - "npm run lint || echo '⚠️ 代码质量检查发现问题，但继续构建'"
    - "echo '🏗️ 生产环境构建...'"
    - "npm run build:prod"
    - "echo '📦 构建产物信息:'"
    - "du -sh dist/"
    - "ls -la dist/"
    - "echo '✅ 前端构建完成'"
  artifacts:
    name: "frontend-$CI_COMMIT_SHORT_SHA"
    paths:
      - dist/
    expire_in: 1 hour
  cache:
    key: frontend-${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
      - .npm/
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

deploy_frontend:
  stage: deploy
  tags:
    - Eva
  dependencies:
    - build_frontend
  before_script:
    - "echo '🚀 准备部署前端到生产环境...'"
    - "echo '📦 验证构建产物...'"
    - "ls -la dist/"
    - "du -sh dist/"
  script:
    - "echo '🔄 部署前端构建产物...'"
    - |
      set -e
      
      # 备份当前版本（如果存在）
      if [ -d "dist.current" ]; then
        echo "📦 备份当前版本..."
        mv dist.current dist.backup.$(date +%Y%m%d_%H%M%S)
        # 保留最近3个备份
        ls -dt dist.backup.* 2>/dev/null | tail -n +4 | xargs rm -rf || true
      fi

      # 移动新构建产物到当前版本
      echo "📂 部署新的构建产物..."
      mv dist dist.current

      echo "🔍 验证构建产物..."
      if [ ! -d "dist.current" ] || [ ! -f "dist.current/index.html" ]; then
        echo "❌ 构建产物验证失败"
        exit 1
      fi

      echo "📊 构建产物信息:"
      du -sh dist.current/
      ls -la dist.current/

      echo "🔄 使用PM2重启前端服务..."
      
      # 停止现有服务
      pm2 stop $SERVICE_NAME 2>/dev/null || echo "前端服务未运行"

      # 启动服务
      pm2 start pm2.config.cjs --only $SERVICE_NAME

      # 保存PM2配置
      pm2 save

      echo "⏳ 等待服务启动..."
      sleep 5

      echo "📊 检查PM2服务状态..."
      pm2 status $SERVICE_NAME

      echo "✅ 前端部署完成"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

health_check_frontend:
  stage: test
  tags:
    - Eva
  dependencies:
    - deploy_frontend
  script:
    - "echo '🏥 执行前端健康检查...'"
    - |
      echo "检查前端服务 (端口 $FRONTEND_PORT)..."
      for i in {1..10}; do
        if curl -f -s "http://localhost:$FRONTEND_PORT" > /dev/null; then
          echo "✅ 前端服务运行正常"
          break
        else
          echo "⏳ 等待前端服务启动... ($i/10)"
          sleep 3
        fi

        if [ $i -eq 10 ]; then
          echo "❌ 前端服务健康检查失败"
          exit 1
        fi
      done

      echo "🔍 检查页面内容..."
      content=$(curl -s "http://localhost:$FRONTEND_PORT" || echo "")
      if echo "$content" | grep -q "<!DOCTYPE html>\|<html"; then
        echo "✅ 前端页面内容正常"
      else
        echo "⚠️ 前端页面内容可能异常"
      fi

      echo "⏱️ 检查响应时间..."
      response_time=$(curl -o /dev/null -s -w "%{time_total}" "http://localhost:$FRONTEND_PORT" || echo "0")
      echo "前端响应时间: ${response_time}秒"

      echo "🎉 前端健康检查完成"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

pm2_status_check:
  stage: test
  tags:
    - Eva
  dependencies:
    - health_check_frontend
  script:
    - "echo '🔍 检查PM2服务状态...'"
    - |
      echo "📊 PM2服务状态:"
      pm2 status

      echo ""
      echo "📋 前端服务详细信息:"
      pm2 show $SERVICE_NAME || echo "⚠️ 无法获取前端服务详细信息"

      echo ""
      echo "📝 前端服务日志 (最近10行):"
      pm2 logs $SERVICE_NAME --lines 10 --nostream || echo "⚠️ 无法获取前端服务日志"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

notify_success:
  stage: notify
  tags:
    - Eva
  dependencies:
    - pm2_status_check
  script:
    - "echo '🎉 前端部署成功通知'"
    - |
      echo "前端部署信息:"
      echo "- 项目: Admin Frontend"
      echo "- 提交: $CI_COMMIT_SHORT_SHA"
      echo "- 分支: $CI_COMMIT_REF_NAME"
      echo "- 服务: $SERVICE_NAME"
      echo "- 访问地址: http://localhost:$FRONTEND_PORT"
      echo "- 流水线: $CI_PIPELINE_URL"
      echo ""
      echo "PM2管理命令:"
      echo "- 查看状态: pm2 status"
      echo "- 查看日志: pm2 logs $SERVICE_NAME"
      echo "- 重启服务: pm2 restart $SERVICE_NAME"
  when: on_success
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

notify_failure:
  stage: notify
  tags:
    - Eva
  script:
    - "echo '❌ 前端部署失败通知'"
    - |
      echo "前端部署失败信息:"
      echo "- 项目: Admin Frontend"
      echo "- 提交: $CI_COMMIT_SHORT_SHA"
      echo "- 分支: $CI_COMMIT_REF_NAME"
      echo "- 流水线: $CI_PIPELINE_URL"
      echo ""
      echo "请检查:"
      echo "1. 构建日志中的错误信息"
      echo "2. PM2服务的运行状态"
      echo "3. 前端服务的日志输出"
      echo "4. 本地环境的配置状态"
  when: on_failure
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/
