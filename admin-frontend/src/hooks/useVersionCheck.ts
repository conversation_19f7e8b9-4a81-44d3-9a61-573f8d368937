import { useState, useEffect, useCallback } from 'react';
import { VersionCheckResponse } from '@/types/system';
import { systemService } from '@/services/system';
import {
  getVersionConfig,
  getCurrentAppVersion,
  STORAGE_KEYS,
  VersionCheckEvents,
  VersionCheckStatus,
  VersionUtils
} from '@/config/version';

interface UseVersionCheckOptions {
  /** 是否自动检查版本 */
  autoCheck?: boolean;
  /** 检查间隔（毫秒），默认24小时 */
  checkInterval?: number;
  /** 是否在应用启动时检查 */
  checkOnMount?: boolean;
  /** 平台类型 */
  platform?: 'ios' | 'android';
}

interface UseVersionCheckReturn {
  /** 版本检查结果 */
  versionInfo: VersionCheckResponse | null;
  /** 是否正在检查版本 */
  isChecking: boolean;
  /** 检查错误信息 */
  error: string | null;
  /** 手动检查版本 */
  checkVersion: () => Promise<void>;
  /** 清除版本检查结果 */
  clearVersionInfo: () => void;
  /** 是否需要更新 */
  needsUpdate: boolean;
  /** 是否为强制更新 */
  isForceUpdate: boolean;
}

// 使用配置文件中的存储键名
const { VERSION_CHECK_DATA, LAST_CHECK_TIME, SKIPPED_VERSION } = STORAGE_KEYS;

/**
 * 版本检测Hook
 * 提供版本检测功能，包括自动检测、手动检测、结果缓存等
 */
export const useVersionCheck = (options: UseVersionCheckOptions = {}): UseVersionCheckReturn => {
  // 获取版本检测配置
  const versionConfig = getVersionConfig();

  const {
    autoCheck = versionConfig.autoCheck,
    checkInterval = versionConfig.checkInterval,
    checkOnMount = versionConfig.checkOnMount,
    platform = versionConfig.platform
  } = options;

  const [versionInfo, setVersionInfo] = useState<VersionCheckResponse | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 从本地存储加载版本检查数据
  const loadStoredVersionInfo = useCallback(() => {
    try {
      const stored = localStorage.getItem(VERSION_CHECK_DATA);
      if (stored) {
        const data = JSON.parse(stored) as VersionCheckResponse;
        setVersionInfo(data);
      }
    } catch (err) {
      console.warn('Failed to load stored version info:', err);
    }
  }, []);

  // 保存版本检查数据到本地存储
  const saveVersionInfo = useCallback((data: VersionCheckResponse) => {
    try {
      localStorage.setItem(VERSION_CHECK_DATA, JSON.stringify(data));
      localStorage.setItem(LAST_CHECK_TIME, Date.now().toString());

      // 触发版本检查完成事件
      window.dispatchEvent(new CustomEvent(VersionCheckEvents.CHECK_COMPLETED, {
        detail: data
      }));
    } catch (err) {
      console.warn('Failed to save version info:', err);
    }
  }, []);

  // 检查是否需要进行版本检测
  const shouldCheckVersion = useCallback(() => {
    if (!autoCheck || !versionConfig.enabled) return false;

    const lastCheckTime = localStorage.getItem(LAST_CHECK_TIME);
    if (!lastCheckTime) return true;

    const timeSinceLastCheck = Date.now() - parseInt(lastCheckTime, 10);
    return timeSinceLastCheck > checkInterval;
  }, [autoCheck, checkInterval, versionConfig.enabled]);

  // 执行版本检查
  const checkVersion = useCallback(async () => {
    if (isChecking || !versionConfig.enabled) return;

    setIsChecking(true);
    setError(null);

    // 触发版本检查开始事件
    window.dispatchEvent(new CustomEvent(VersionCheckEvents.CHECK_STARTED));

    try {
      const result = await systemService.checkVersion(platform);
      setVersionInfo(result);
      saveVersionInfo(result);

      // 触发相应的事件
      if (result.needUpdate) {
        const eventType = result.updateType === 'force'
          ? VersionCheckEvents.UPDATE_REQUIRED
          : VersionCheckEvents.UPDATE_AVAILABLE;
        window.dispatchEvent(new CustomEvent(eventType, { detail: result }));
      }

      console.log('Version check completed:', VersionUtils.createCheckLog({
        status: result.needUpdate ? VersionCheckStatus.UPDATE_AVAILABLE : VersionCheckStatus.UP_TO_DATE,
        currentVersion: getCurrentAppVersion(),
        latestVersion: result.latestVersion,
        minimumVersion: result.minimumVersion,
        updateType: result.updateType as any,
        updateMessage: result.updateMessage,
        downloadUrl: result.downloadUrl,
        needsUpdate: result.needUpdate,
        isForceUpdate: result.updateType === 'force',
        isBelowMinimum: result.versionInfo?.isBelowMinimum ?? false,
        checkTime: Date.now(),
      }));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '版本检查失败';
      setError(errorMessage);

      // 触发版本检查失败事件
      window.dispatchEvent(new CustomEvent(VersionCheckEvents.CHECK_FAILED, {
        detail: { error: errorMessage }
      }));

      console.error('Version check failed:', err);
    } finally {
      setIsChecking(false);
    }
  }, [isChecking, platform, saveVersionInfo, versionConfig.enabled]);

  // 清除版本检查结果
  const clearVersionInfo = useCallback(() => {
    setVersionInfo(null);
    setError(null);
    localStorage.removeItem(VERSION_CHECK_DATA);
    localStorage.removeItem(LAST_CHECK_TIME);
  }, []);

  // 计算是否需要更新
  const needsUpdate = versionInfo?.needUpdate ?? false;

  // 计算是否为强制更新
  const isForceUpdate = versionInfo?.updateType === 'force';

  // 检查是否已跳过当前版本
  const isVersionSkipped = useCallback((version: string) => {
    const skippedVersion = localStorage.getItem(SKIPPED_VERSION);
    return skippedVersion === version;
  }, []);

  // 跳过版本
  const skipVersion = useCallback((version: string) => {
    localStorage.setItem(SKIPPED_VERSION, version);

    // 触发跳过版本事件
    window.dispatchEvent(new CustomEvent(VersionCheckEvents.UPDATE_SKIPPED, {
      detail: { version }
    }));
  }, []);

  // 组件挂载时的初始化
  useEffect(() => {
    // 加载存储的版本信息
    loadStoredVersionInfo();

    // 如果启用了挂载时检查，且需要检查版本，则执行检查
    if (checkOnMount && shouldCheckVersion()) {
      checkVersion();
    }
  }, [loadStoredVersionInfo, checkOnMount, shouldCheckVersion, checkVersion]);

  // 自动检查定时器
  useEffect(() => {
    if (!autoCheck) return;

    const timer = setInterval(() => {
      if (shouldCheckVersion()) {
        checkVersion();
      }
    }, checkInterval);

    return () => clearInterval(timer);
  }, [autoCheck, shouldCheckVersion, checkVersion, checkInterval]);

  // 监听页面可见性变化，页面重新可见时检查版本
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && shouldCheckVersion()) {
        checkVersion();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [shouldCheckVersion, checkVersion]);

  return {
    versionInfo,
    isChecking,
    error,
    checkVersion,
    clearVersionInfo,
    needsUpdate,
    isForceUpdate,
  };
};

/**
 * 版本比较工具函数
 * @param version1 版本号1
 * @param version2 版本号2
 * @returns 1: version1 > version2, 0: 相等, -1: version1 < version2
 */
export const compareVersions = (version1: string, version2: string): number => {
  const v1Parts = version1.split('.').map(Number);
  const v2Parts = version2.split('.').map(Number);
  
  // 确保版本号都有三个部分（major.minor.patch）
  while (v1Parts.length < 3) v1Parts.push(0);
  while (v2Parts.length < 3) v2Parts.push(0);
  
  for (let i = 0; i < 3; i++) {
    if (v1Parts[i] > v2Parts[i]) return 1;
    if (v1Parts[i] < v2Parts[i]) return -1;
  }
  
  return 0;
};

/**
 * 获取当前应用版本
 * @returns 当前应用版本号
 */
export const getCurrentVersion = (): string => {
  // 从package.json或环境变量获取版本号
  return process.env.REACT_APP_VERSION || '1.0.0';
};

/**
 * 版本检测工具类
 */
export class VersionChecker {
  private static instance: VersionChecker;
  private checkPromise: Promise<VersionCheckResponse> | null = null;

  static getInstance(): VersionChecker {
    if (!VersionChecker.instance) {
      VersionChecker.instance = new VersionChecker();
    }
    return VersionChecker.instance;
  }

  /**
   * 单例版本检查，避免重复请求
   */
  async checkVersion(platform: 'ios' | 'android' = 'ios'): Promise<VersionCheckResponse> {
    if (this.checkPromise) {
      return this.checkPromise;
    }

    this.checkPromise = systemService.checkVersion(platform);
    
    try {
      const result = await this.checkPromise;
      return result;
    } finally {
      this.checkPromise = null;
    }
  }

  /**
   * 清除检查缓存
   */
  clearCache(): void {
    this.checkPromise = null;
    localStorage.removeItem(VERSION_CHECK_STORAGE_KEY);
    localStorage.removeItem(LAST_CHECK_TIME_KEY);
  }
}

export default useVersionCheck;
