import { request } from './api';
import { LoginCredentials, AuthResponse, AuthUser } from '@/types/common';
import { responseHandlers } from '@/utils/apiResponseHandler';
import { authStorage } from '@/utils/storageManager';

export const authService = {
  // 管理员登录
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    const response = await request.post<AuthResponse>('/admin/auth/login', credentials);
    const authData = responseHandlers.get(response, '登录失败');

    // 使用统一的存储管理器保存认证信息
    authStorage.setToken(authData.token);
    authStorage.setUser(authData.user);
    authStorage.setLoginTimestamp();

    return authData;
  },

  // 管理员登出
  logout: async (skipServerRequest: boolean = false): Promise<void> => {
    // 如果不跳过服务器请求且有有效token，则尝试通知服务器
    if (!skipServerRequest && authStorage.getToken()) {
      try {
        const response = await request.post('/admin/auth/logout');
        responseHandlers.get(response);
      } catch (error) {
        // 即使服务器返回错误，也要清除本地存储
        console.warn('登出请求失败:', error);
      }
    }

    // 使用统一的存储管理器清除认证信息
    authStorage.clearAuth();
  },

  // 获取当前管理员信息
  getCurrentUser: async (): Promise<AuthUser> => {
    const response = await request.get<AuthUser>('/admin/auth/me');
    const userData = responseHandlers.get(response, '获取用户信息失败');

    // 更新本地存储的用户信息
    authStorage.setUser(userData);
    return userData;
  },

  // 刷新token
  refreshToken: async (): Promise<string> => {
    const response = await request.post<{ token: string }>('/admin/auth/refresh');
    const tokenData = responseHandlers.get(response, '刷新token失败');

    authStorage.setToken(tokenData.token);
    return tokenData.token;
  },

  // 修改密码
  changePassword: async (data: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<void> => {
    const response = await request.put('/admin/auth/password', data);
    return responseHandlers.update(response, '密码修改成功');
  },

  // 检查是否已登录
  isAuthenticated: (): boolean => {
    return authStorage.isAuthenticated();
  },

  // 获取本地存储的用户信息
  getStoredUser: (): AuthUser | null => {
    return authStorage.getUser<AuthUser>();
  },

  // 获取本地存储的token
  getStoredToken: (): string | null => {
    return authStorage.getToken();
  },
};
