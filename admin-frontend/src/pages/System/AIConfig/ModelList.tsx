import React, { useState, useEffect } from 'react';
import {
  ArrowPathIcon,
  PencilIcon,
  CheckCircleIcon,
  XCircleIcon,
  CurrencyDollarIcon,
  XMarkIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { AIServiceConfig, AIServiceModel } from '../../../types/aiConfig';
import { aiConfigService } from '../../../services/aiConfigService';
import { useToast } from '../../../hooks/useToast';

interface ModelListProps {
  configs: AIServiceConfig[];
  onRefresh: () => void;
}

const ModelList: React.FC<ModelListProps> = ({ configs, onRefresh }) => {
  const [selectedConfigId, setSelectedConfigId] = useState<string>('');
  const [models, setModels] = useState<AIServiceModel[]>([]);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [disabling, setDisabling] = useState(false);
  const [togglingStatus, setTogglingStatus] = useState<string | null>(null);
  const [activatingModel, setActivatingModel] = useState<string | null>(null);
  const [editingModel, setEditingModel] = useState<AIServiceModel | null>(null);
  const { showToast } = useToast();

  // 加载模型列表
  const loadModels = async (configId: string) => {
    if (!configId) {
      setModels([]);
      return;
    }

    try {
      setLoading(true);
      const response = await aiConfigService.getModels({ configId, limit: 100 });
      setModels(response.data.models);
    } catch (error) {
      showToast('加载模型列表失败', 'error');
      console.error('加载模型失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 同步模型
  const handleSyncModels = async () => {
    if (!selectedConfigId) return;

    try {
      setSyncing(true);
      await aiConfigService.syncModels(selectedConfigId);
      showToast('模型同步成功', 'success');
      await loadModels(selectedConfigId);
    } catch (error) {
      showToast('模型同步失败', 'error');
      console.error('同步模型失败:', error);
    } finally {
      setSyncing(false);
    }
  };

  // 一键全部禁用模型
  const handleDisableAllModels = async () => {
    if (!selectedConfigId) return;

    // 确认对话框
    if (!window.confirm('确定要禁用所有模型吗？此操作将禁用当前配置下的所有模型。')) {
      return;
    }

    try {
      setDisabling(true);
      const response = await fetch(`/api/admin/model-pricing/disable-all/${selectedConfigId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('禁用模型失败');
      }

      const result = await response.json();
      showToast(`成功禁用 ${result.data.disabledCount} 个模型`, 'success');
      await loadModels(selectedConfigId);
    } catch (error) {
      console.error('禁用模型失败:', error);
      showToast('禁用模型失败', 'error');
    } finally {
      setDisabling(false);
    }
  };

  // 切换模型状态
  const handleToggleModelStatus = async (model: AIServiceModel) => {
    try {
      setTogglingStatus(model._id);
      const response = await fetch(`/api/admin/model-pricing/toggle-status/${model._id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (!response.ok) {
        throw new Error('切换模型状态失败');
      }

      const result = await response.json();
      showToast(result.message, 'success');
      await loadModels(selectedConfigId);
    } catch (error) {
      console.error('切换模型状态失败:', error);
      showToast('切换模型状态失败', 'error');
    } finally {
      setTogglingStatus(null);
    }
  };

  // 设为当前模型
  const handleActivateModel = async (model: AIServiceModel) => {
    if (!model.isActive) {
      showToast('请先启用该模型', 'error');
      return;
    }

    try {
      setActivatingModel(model._id);
      const response = await fetch(`/api/admin/ai/configs/${selectedConfigId}/activate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({
          modelId: model._id
        })
      });

      if (!response.ok) {
        throw new Error('设置当前模型失败');
      }

      const result = await response.json();
      showToast('已设为当前模型', 'success');
      await loadModels(selectedConfigId);
    } catch (error) {
      console.error('设置当前模型失败:', error);
      showToast('设置当前模型失败', 'error');
    } finally {
      setActivatingModel(null);
    }
  };

  // 更新模型
  const handleUpdateModel = async (model: AIServiceModel, updates: Partial<AIServiceModel>) => {
    try {
      await aiConfigService.updateModel(model._id, updates);
      showToast('模型更新成功', 'success');
      await loadModels(selectedConfigId);
      setEditingModel(null);
    } catch (error) {
      showToast('模型更新失败', 'error');
      console.error('更新模型失败:', error);
    }
  };

  useEffect(() => {
    if (configs.length > 0 && !selectedConfigId) {
      const defaultConfig = configs.find(c => c.isDefault) || configs[0];
      setSelectedConfigId(defaultConfig._id);
    }
  }, [configs]);

  useEffect(() => {
    loadModels(selectedConfigId);
  }, [selectedConfigId]);

  const selectedConfig = configs.find(c => c._id === selectedConfigId);

  const formatPrice = (price: number, currency: string) => {
    // 统一以5元/3000字符为基础单位显示
    const basePrice = 5 / 3000; // 5元每3000字符
    const displayPrice = (price / basePrice * 5).toFixed(2);
    return `${displayPrice} ${currency}/3000字符`;
  };

  const getFeatureBadges = (features: string[]) => {
    const colors = {
      text: 'bg-blue-100 text-blue-800',
      image: 'bg-green-100 text-green-800',
      function_calling: 'bg-purple-100 text-purple-800',
      streaming: 'bg-yellow-100 text-yellow-800',
      vision: 'bg-pink-100 text-pink-800',
      audio: 'bg-indigo-100 text-indigo-800'
    };

    return features.map(feature => (
      <span
        key={feature}
        className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
          colors[feature] || 'bg-gray-100 text-gray-800'
        }`}
      >
        {feature}
      </span>
    ));
  };

  return (
    <div className="space-y-6">
      {/* 配置选择 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">
              选择配置
            </label>
            <select
              value={selectedConfigId}
              onChange={(e) => setSelectedConfigId(e.target.value)}
              className="input mt-1"
            >
              <option value="">请选择配置</option>
              {configs.map(config => (
                <option key={config._id} value={config._id}>
                  {config.name} ({config.provider})
                </option>
              ))}
            </select>
          </div>
          
          {selectedConfig && (
            <div className="pt-6 flex space-x-3">
              <button
                onClick={handleSyncModels}
                disabled={syncing || disabling}
                className="btn btn-primary"
              >
                {syncing ? (
                  <>
                    <div className="spinner h-4 w-4 mr-2"></div>
                    同步中...
                  </>
                ) : (
                  <>
                    <ArrowPathIcon className="h-4 w-4 mr-2" />
                    同步模型
                  </>
                )}
              </button>

              <button
                onClick={handleDisableAllModels}
                disabled={syncing || disabling}
                className="btn btn-secondary"
              >
                {disabling ? (
                  <>
                    <div className="spinner h-4 w-4 mr-2"></div>
                    禁用中...
                  </>
                ) : (
                  <>
                    <XMarkIcon className="h-4 w-4 mr-2" />
                    一键全部禁用
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* 模型列表 */}
      {selectedConfigId && (
        <div className="card">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="spinner h-8 w-8"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th>模型名称</th>
                    <th>支持功能</th>
                    <th>最大Token</th>
                    <th>定价</th>
                    <th>模型信息</th>
                    <th>状态</th>
                    <th>当前模型</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {models.map((model) => (
                    <tr key={model._id} className="table-row">
                      <td>
                        <div>
                          <div className="font-medium text-gray-900">
                            {model.displayName}
                          </div>
                          <div className="text-sm text-gray-500 font-mono">
                            {model.modelName}
                          </div>
                          {model.description && (
                            <div className="text-sm text-gray-500 mt-1">
                              {model.description}
                            </div>
                          )}
                        </div>
                      </td>
                      <td>
                        <div className="flex flex-wrap gap-1">
                          {getFeatureBadges(model.supportedFeatures)}
                        </div>
                      </td>
                      <td>
                        <span className="text-sm text-gray-900">
                          {model.maxTokens.toLocaleString()}
                        </span>
                      </td>
                      <td>
                        <div className="text-sm">
                          <div className="flex items-center text-gray-900">
                            <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                            输入: {formatPrice(model.pricing.inputPrice, model.pricing.currency)}
                            {model.isPricingCustomized && (
                              <span className="ml-1 text-xs bg-blue-100 text-blue-800 px-1 rounded" title="手动修改">
                                自定义
                              </span>
                            )}
                          </div>
                          <div className="flex items-center text-gray-500">
                            <CurrencyDollarIcon className="h-4 w-4 mr-1" />
                            输出: {formatPrice(model.pricing.outputPrice, model.pricing.currency)}
                          </div>
                        </div>
                      </td>
                      <td>
                        {model.modelInfo ? (
                          <details className="text-xs">
                            <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                              查看详情
                            </summary>
                            <pre className="mt-2 p-2 bg-gray-50 rounded text-xs overflow-auto max-h-32 max-w-xs">
                              {JSON.stringify(model.modelInfo, null, 2)}
                            </pre>
                          </details>
                        ) : (
                          <span className="text-gray-400 text-xs">无信息</span>
                        )}
                      </td>
                      <td>
                        <button
                          onClick={() => handleToggleModelStatus(model)}
                          disabled={togglingStatus === model._id}
                          className={`badge cursor-pointer transition-colors duration-200 ${
                            model.isActive
                              ? 'badge-success hover:bg-green-600'
                              : 'badge-danger hover:bg-red-600'
                          } ${togglingStatus === model._id ? 'opacity-50 cursor-not-allowed' : ''}`}
                          title={`点击${model.isActive ? '禁用' : '启用'}模型`}
                        >
                          {togglingStatus === model._id ? (
                            <>
                              <div className="spinner h-3 w-3 mr-1"></div>
                              切换中...
                            </>
                          ) : model.isActive ? (
                            <>
                              <CheckCircleIcon className="h-4 w-4 mr-1" />
                              启用
                            </>
                          ) : (
                            <>
                              <XCircleIcon className="h-4 w-4 mr-1" />
                              禁用
                            </>
                          )}
                        </button>
                      </td>
                      <td>
                        {model.isActive ? (
                          <button
                            onClick={() => handleActivateModel(model)}
                            disabled={activatingModel === model._id}
                            className="btn btn-sm btn-outline-primary"
                            title="设为当前模型"
                          >
                            {activatingModel === model._id ? (
                              <>
                                <div className="spinner h-3 w-3 mr-1"></div>
                                设置中...
                              </>
                            ) : (
                              <>
                                <StarIcon className="h-4 w-4 mr-1" />
                                设为当前
                              </>
                            )}
                          </button>
                        ) : (
                          <span className="text-gray-400 text-sm">需先启用</span>
                        )}
                      </td>
                      <td>
                        <button
                          onClick={() => setEditingModel(model)}
                          className="text-indigo-600 hover:text-indigo-900"
                          title="编辑"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {models.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-gray-500">
                    {selectedConfigId ? '暂无模型，请先同步模型列表' : '请选择配置'}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* 编辑模型对话框 */}
      {editingModel && (
        <ModelEditDialog
          model={editingModel}
          onSave={(updates) => handleUpdateModel(editingModel, updates)}
          onCancel={() => setEditingModel(null)}
        />
      )}
    </div>
  );
};

// 模型编辑对话框组件
const ModelEditDialog: React.FC<{
  model: AIServiceModel;
  onSave: (updates: Partial<AIServiceModel>) => void;
  onCancel: () => void;
}> = ({ model, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    displayName: model.displayName,
    description: model.description || '',
    maxTokens: model.maxTokens,
    inputPrice: model.pricing.inputPrice,
    outputPrice: model.pricing.outputPrice,
    isActive: model.isActive
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      displayName: formData.displayName,
      description: formData.description || undefined,
      maxTokens: formData.maxTokens,
      pricing: {
        ...model.pricing,
        inputPrice: formData.inputPrice,
        outputPrice: formData.outputPrice
      },
      isActive: formData.isActive
    });
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-lg shadow-lg rounded-md bg-white">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          编辑模型 - {model.modelName}
        </h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">显示名称</label>
            <input
              type="text"
              value={formData.displayName}
              onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
              className="input mt-1"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">描述</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="input mt-1"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">最大Token</label>
              <input
                type="number"
                value={formData.maxTokens}
                onChange={(e) => setFormData(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
                className="input mt-1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">状态</label>
              <select
                value={formData.isActive ? 'active' : 'inactive'}
                onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.value === 'active' }))}
                className="input mt-1"
              >
                <option value="active">启用</option>
                <option value="inactive">禁用</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">输入价格</label>
              <input
                type="number"
                step="0.0001"
                value={formData.inputPrice}
                onChange={(e) => setFormData(prev => ({ ...prev, inputPrice: parseFloat(e.target.value) }))}
                className="input mt-1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">输出价格</label>
              <input
                type="number"
                step="0.0001"
                value={formData.outputPrice}
                onChange={(e) => setFormData(prev => ({ ...prev, outputPrice: parseFloat(e.target.value) }))}
                className="input mt-1"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button type="button" onClick={onCancel} className="btn btn-secondary">
              取消
            </button>
            <button type="submit" className="btn btn-primary">
              保存
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ModelList;
