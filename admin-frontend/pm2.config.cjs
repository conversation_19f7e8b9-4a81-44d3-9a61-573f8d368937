// ChatAdvisor Admin Frontend PM2 配置
// 专用于前端服务的独立部署和管理

module.exports = {
    apps: [
        {
            name: 'admin-frontend-release',
            script: 'npx',
            args: 'vite preview --port 54001 --host',
            cwd: process.cwd(),
            watch: false,
            autorestart: true,
            max_restarts: 10,
            min_uptime: '10s',
            env: {
                NODE_ENV: 'production',
                PORT: 54001,
                HOST: '0.0.0.0'
            },
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            log_file: 'logs/admin-release-combined.log',
            error_file: 'logs/admin-release-error.log',
            out_file: 'logs/admin-release-out.log',
            merge_logs: true,
            instances: 1,
            exec_mode: 'fork',
            // 健康检查配置
            health_check_url: 'http://localhost:54001',
            health_check_grace_period: 3000,
        },
        {
            name: 'admin-frontend-debug',
            script: 'npx',
            args: 'vite --port 34001 --host',
            cwd: process.cwd(),
            watch: false,
            autorestart: true,
            max_restarts: 10,
            min_uptime: '10s',
            env: {
                NODE_ENV: 'development',
                PORT: 34001,
                HOST: '0.0.0.0'
            },
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            log_file: 'logs/admin-debug-combined.log',
            error_file: 'logs/admin-debug-error.log',
            out_file: 'logs/admin-debug-out.log',
            merge_logs: true,
            instances: 1,
            exec_mode: 'fork',
            // 健康检查配置
            health_check_url: 'http://localhost:34001',
            health_check_grace_period: 3000,
        }
    ],
    
    // 部署配置
    deploy: {
        production: {
            user: 'root',
            host: '**************',
            ref: 'origin/main',
            repo: '**********************:server/admin-frontend.git',
            path: '/opt/chatadvisor/admin-frontend',
            'pre-deploy-local': '',
            'post-deploy': 'npm install && npm run build:prod && pm2 reload ecosystem.config.js --env production',
            'pre-setup': ''
        }
    }
};
