# 服务器配置
NODE_ENV=development
PORT=33001

# xAI API 配置
OPENAI_API_KEY=your_xai_api_key_here
OPENAI_BASE_URL=https://api.x.ai/v1/

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/ChatAdvisor_test
MONGODB_URI_PRODUCTION=mongodb://localhost:27017/ChatAdvisor

# JWT 配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=604800

# 第三方登录配置
GOOGLE_CLIENT_ID=your_google_client_id_here

TWITTER_API_KEY=your_twitter_api_key_here
TWITTER_API_SECRET=your_twitter_api_secret_here

TIKTOK_CLIENT_ID=your_tiktok_client_id_here
TIKTOK_CLIENT_SECRET=your_tiktok_client_secret_here

FACEBOOK_APP_ID=your_facebook_app_id_here
FACEBOOK_APP_SECRET=your_facebook_app_secret_here

# 应用配置
BASE_URL=https://advisor.sanva.tk
APP_VERSION=1.0
SUPPORT_EMAIL=<EMAIL>
DEFAULT_BALANCE=50
PROFIT_RATE=0.045
EXCHANGE_RATE=100
NEED_TO_ENCRYPT=0

# CORS配置
ALLOWED_ORIGINS=http://localhost:3000,https://advisor.sanva.tk,https://advisor.sanva.top

# 管理员密码配置
ADMIN_PASSWORD=your_admin_password_here
MODERATOR_PASSWORD=your_moderator_password_here

#代理配置 http://127.0.0.1:7890
HTTP_PROXY=http://127.0.0.1:7890
HTTPS_PROXY=http://127.0.0.1:7890

# 邮件配置（如果需要）
SMTP_HOST=your_smtp_host_here
SMTP_PORT=587
SMTP_USER=your_smtp_user_here
SMTP_PASS=your_smtp_password_here
