/**
 * 版本控制字段迁移脚本
 * 为现有的Config文档添加版本控制相关字段的默认值
 */

import mongoose from 'mongoose';
import { Config } from '../models/Config';
import { logger } from '../utils/logger';
import connectDatabase from '../config/database';

/**
 * 执行版本控制字段迁移
 */
async function migrateVersionControlFields(): Promise<void> {
    try {
        logger.info('开始执行版本控制字段迁移...');
        
        // 连接数据库
        await connectDatabase();
        
        // 查找所有现有的配置文档
        const configs = await Config.find({});
        
        if (configs.length === 0) {
            logger.info('未找到现有配置，创建默认配置...');
            
            // 创建默认配置
            const defaultConfig = new Config({
                privacyPolicy: 'Default privacy policy content',
                termsOfService: 'Default terms of service content',
                appVersion: '1.0.0',
                supportEmail: '<EMAIL>',
                featureFlags: new Map([
                    ['enableRegistration', true],
                    ['enableChat', true],
                    ['enablePayment', true]
                ]),
                mainSolgan: new Map([
                    ['zh_CN', ['欢迎使用ChatAdvisor', '智能对话助手']],
                    ['en', ['Welcome to ChatAdvisor', 'Intelligent Chat Assistant']]
                ]),
                registerSolgan: new Map([
                    ['zh_CN', ['立即注册', '开始您的智能对话之旅']],
                    ['en', ['Register Now', 'Start Your Intelligent Chat Journey']]
                ]),
                emailLoginSolgan: new Map([
                    ['zh_CN', ['邮箱登录', '安全便捷']],
                    ['en', ['Email Login', 'Secure and Convenient']]
                ]),
                rechargeMessages: new Map([
                    ['zh_CN', ['充值成功', '余额已更新']],
                    ['en', ['Recharge Successful', 'Balance Updated']]
                ]),
                hideMessage: new Map([
                    ['zh_CN', ['消息已隐藏']],
                    ['en', ['Message Hidden']]
                ]),
                rechargeDescription: new Map([
                    ['zh_CN', '充值说明：请选择合适的充值金额'],
                    ['en', 'Recharge Description: Please select an appropriate amount']
                ]),
                promotLocal: new Map([
                    ['zh_CN', '本地推广内容'],
                    ['en', 'Local promotion content']
                ]),
                promotCloud: new Map([
                    ['zh_CN', '云端推广内容'],
                    ['en', 'Cloud promotion content']
                ]),
                compressRate: 0.8,
                // 版本控制相关的默认值
                latestVersion: '1.0.0',
                minimumVersion: '1.0.0',
                forceUpdate: false,
                updateMessage: new Map([
                    ['zh_CN', '发现新版本，建议立即更新以获得更好的体验'],
                    ['en', 'New version available, please update for better experience']
                ]),
                appStoreUrls: {
                    ios: 'APP_STORE_URL_PLACEHOLDER',
                    android: 'GOOGLE_PLAY_URL_PLACEHOLDER'
                },
                updateType: 'optional',
                versionCheckEnabled: true
            });
            
            await defaultConfig.save();
            logger.info('默认配置创建成功');
            
        } else {
            logger.info(`找到 ${configs.length} 个现有配置，开始迁移...`);
            
            for (const config of configs) {
                let needUpdate = false;
                
                // 检查并添加缺失的版本控制字段
                if (!config.latestVersion) {
                    config.latestVersion = '1.0.0';
                    needUpdate = true;
                }
                
                if (!config.minimumVersion) {
                    config.minimumVersion = '1.0.0';
                    needUpdate = true;
                }
                
                if (config.forceUpdate === undefined) {
                    config.forceUpdate = false;
                    needUpdate = true;
                }
                
                if (!config.updateMessage || config.updateMessage.size === 0) {
                    config.updateMessage = new Map([
                        ['zh_CN', '发现新版本，建议立即更新以获得更好的体验'],
                        ['en', 'New version available, please update for better experience']
                    ]);
                    needUpdate = true;
                }
                
                if (!config.appStoreUrls) {
                    config.appStoreUrls = {
                        ios: 'APP_STORE_URL_PLACEHOLDER',
                        android: 'GOOGLE_PLAY_URL_PLACEHOLDER'
                    };
                    needUpdate = true;
                }
                
                if (!config.updateType) {
                    config.updateType = 'optional';
                    needUpdate = true;
                }
                
                if (config.versionCheckEnabled === undefined) {
                    config.versionCheckEnabled = true;
                    needUpdate = true;
                }
                
                if (needUpdate) {
                    await config.save();
                    logger.info(`配置 ${config._id} 迁移完成`);
                } else {
                    logger.info(`配置 ${config._id} 无需迁移`);
                }
            }
        }
        
        logger.info('版本控制字段迁移完成');
        
    } catch (error) {
        logger.error('版本控制字段迁移失败:', error);
        throw error;
    }
}

/**
 * 验证迁移结果
 */
async function validateMigration(): Promise<void> {
    try {
        logger.info('开始验证迁移结果...');
        
        const configs = await Config.find({});
        
        for (const config of configs) {
            const requiredFields = [
                'latestVersion',
                'minimumVersion',
                'forceUpdate',
                'updateMessage',
                'appStoreUrls',
                'updateType',
                'versionCheckEnabled'
            ];
            
            const missingFields = requiredFields.filter(field => {
                const value = (config as any)[field];
                return value === undefined || value === null;
            });
            
            if (missingFields.length > 0) {
                logger.error(`配置 ${config._id} 缺少字段: ${missingFields.join(', ')}`);
                throw new Error(`迁移验证失败：配置 ${config._id} 缺少必需的版本控制字段`);
            }
            
            // 验证版本号格式
            const versionRegex = /^\d+\.\d+\.\d+$/;
            if (!versionRegex.test(config.latestVersion)) {
                throw new Error(`配置 ${config._id} 的 latestVersion 格式不正确: ${config.latestVersion}`);
            }
            
            if (!versionRegex.test(config.minimumVersion)) {
                throw new Error(`配置 ${config._id} 的 minimumVersion 格式不正确: ${config.minimumVersion}`);
            }
            
            // 验证updateType值
            if (!['force', 'optional'].includes(config.updateType)) {
                throw new Error(`配置 ${config._id} 的 updateType 值不正确: ${config.updateType}`);
            }
            
            logger.info(`配置 ${config._id} 验证通过`);
        }
        
        logger.info('迁移结果验证完成，所有配置都包含必需的版本控制字段');
        
    } catch (error) {
        logger.error('迁移验证失败:', error);
        throw error;
    }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
    try {
        await migrateVersionControlFields();
        await validateMigration();
        
        logger.info('版本控制字段迁移和验证全部完成');
        process.exit(0);
        
    } catch (error) {
        logger.error('迁移过程失败:', error);
        process.exit(1);
    } finally {
        // 关闭数据库连接
        await mongoose.connection.close();
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

export {
    migrateVersionControlFields,
    validateMigration
};
