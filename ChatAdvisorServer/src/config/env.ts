import crypto from 'crypto';
import 'dotenv/config';

// 直接从环境变量读取，避免循环依赖
const nodeEnv = process.env.NODE_ENV || 'development';

// 兼容性导出 - 逐步迁移到新的配置系统
const env = {
    isProduction: nodeEnv === 'production',
    isDevelopment: nodeEnv === 'development',
    isTest: nodeEnv === 'test',
    isRelease: nodeEnv === 'production'
};

// 加密
export const needToEncrypt = parseInt(process.env.NEED_TO_ENCRYPT || '0');
// 端口
export const port = process.env.PORT || '3000';

// 秘钥 - 使用相同的编码格式和哈希算法保持兼容性
const originalSecretKey = process.env.JWT_SECRET || 'default-secret-key-change-in-production';
const hash = crypto.createHash('sha256');
hash.update(originalSecretKey);
export const secretKey = hash.digest('hex');

// token有效期
export const tokenExpiresIn = parseInt(process.env.JWT_EXPIRES_IN || '604800');

// 创建用户时给的默认余额
export const defaultBalance = parseInt(process.env.DEFAULT_BALANCE || '50');

export const ENV_MONGODB_URI = env.isRelease
    ? (process.env.MONGODB_URI_PRODUCTION || 'mongodb://localhost:27017/ChatAdvisor')
    : (process.env.MONGODB_URI || 'mongodb://localhost:27017/ChatAdvisor_test');

// 利润
export const profitRate = parseFloat(process.env.PROFIT_RATE || '0.045');
// 汇率
export const exchangeRate = parseInt(process.env.EXCHANGE_RATE || '100');

export const googleClientId = process.env.GOOGLE_CLIENT_ID || '';

export const twitterApiKey = process.env.TWITTER_API_KEY || '';
export const twitterApiSecret = process.env.TWITTER_API_SECRET || '';

export const tiktokClientId = process.env.TIKTOK_CLIENT_ID || '';
export const tiktokClientSecret = process.env.TIKTOK_CLIENT_SECRET || '';

export default env;

