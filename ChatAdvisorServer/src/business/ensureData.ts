import Pricing from '../models/Pricing';
import { Question } from '../models/Question';
import { Product } from '../models/Product';
import { Config } from '../models/Config';
import User from '../models/User';
import pricingData from '../config/Pricing';
import questionsData from '../config/Questions';
import config from '../config/Config';
import { logger } from './logger';
import bcrypt from 'bcryptjs';

export async function ensureData() {
    await Pricing.deleteMany({}).exec();

    for (const item of pricingData) {
        // 重新添加
        const newItem = new Pricing(item);
        await newItem.save();
        logger.debug(`增加模型: ${item.alias.zh} ${item.modelName}`);
    }

    

    const products = [
        { productIdentifier: 'com.sanva.advisor.1', amount: 99, isEnable: true, nation: 'en' },
        { productIdentifier: 'com.sanva.advisor.2', amount: 199, isEnable: true, nation: 'en' },
        // 3填错了，用31.。。
        { productIdentifier: 'com.sanva.advisor.31', amount: 299, isEnable: true, nation: 'en' },
        { productIdentifier: 'com.sanva.advisor.4', amount: 399, isEnable: true, nation: 'en' },
        { productIdentifier: 'com.sanva.advisor.5', amount: 499, isEnable: true, nation: 'en' },
        { productIdentifier: 'com.sanva.advisor.6', amount: 599, isEnable: true, nation: 'en' },
        { productIdentifier: 'com.sanva.advisor.7', amount: 699, isEnable: true, nation: 'en' },
        { productIdentifier: 'com.sanva.advisor.8', amount: 799, isEnable: true, nation: 'en' },
        { productIdentifier: 'com.sanva.advisor.9', amount: 899, isEnable: true, nation: 'en' }
    ];

    for (const item of questionsData) {
        const exists = await Question.findOne({ sketch: item.sketch }).exec();
        if (!exists) {
            const newItem = new Question(item);
            await newItem.save();
        } else {
            // update price
            await Question.updateOne(
                { sketch: item.sketch },
                {
                    content: item.content,
                    question: item.question
                }
            ).exec();
        }
    }

    for (const item of products) {
        const exists = await Product.findOne({ productIdentifier: item.productIdentifier }).exec();
        if (!exists) {
            const newItem = new Product(item);
            await newItem.save();
        } else {
            // update price
            await Product.updateOne(
                { productIdentifier: item.productIdentifier },
                {
                    amount: item.amount,
                    isEnable: item.isEnable
                }
            ).exec();
        }
    }

    const exists = await Config.findOne({ appVersion: config.appVersion }).exec();

    if (!exists) {
        await config.save();
    } else {
        // 除了id以外都更新
        await Config.updateOne(
            { appVersion: config.appVersion },
            {
                privacyPolicy: config.privacyPolicy,
                termsOfService: config.termsOfService,
                supportEmail: config.supportEmail,
                featureFlags: config.featureFlags,
                mainSolgan: config.mainSolgan,
                registerSolgan: config.registerSolgan,
                emailLoginSolgan: config.emailLoginSolgan,
                rechargeMessages: config.rechargeMessages,
                hideMessage: config.hideMessage,
                rechargeDescription: config.rechargeDescription,
                promotLocal: config.promotLocal,
                promotCloud: config.promotCloud,
                compressRate: config.compressRate,
            }
        ).exec();
    }

    // 创建默认管理员用户
    await ensureAdminUser();
}

/**
 * 确保存在默认管理员用户
 */
async function ensureAdminUser() {
    try {
        // 检查是否已存在管理员用户
        const adminExists = await User.findOne({
            email: '<EMAIL>'
        }).exec();

        if (!adminExists) {
            // 创建默认管理员用户
            const adminPassword = process.env.ADMIN_PASSWORD || 'admin123456';
            const hashedPassword = await bcrypt.hash(adminPassword, 12);

            const adminUser = new User({
                email: '<EMAIL>',
                password: hashedPassword,
                fullName: 'System Administrator',
                role: 'super_admin',
                status: 'active',
                isVip: true,
                balance: 0,
                createdAt: new Date(),
                updatedAt: new Date()
            });

            await adminUser.save();
            logger.info(`Default admin user created: <EMAIL> / ${adminPassword}`);
        } else {
            logger.debug('Admin user already exists, skipping creation');
        }
    } catch (error: any) {
        // 如果是重复键错误，说明用户已存在，这是正常的
        if (error.code === 11000) {
            logger.debug('Admin user already exists (duplicate key), skipping creation');
        } else {
            logger.error('Error creating admin user:', error);
        }
    }
}
