import mongoose, { Document, Model, Schema } from 'mongoose';
import UserBalance from './Balance';
import { defaultBalance } from '../config/env';
interface AppleInfo extends Document {
    iss: string;
    aud: string;
    exp: number;
    iat: number;
    sub: string;
    c_hash: string;
    email: string;
    email_verified: boolean;
    is_private_email: boolean;
    auth_time: number;
    nonce_supported: boolean;
}

interface GoogleInfo extends Document {
    iss: string;
    azp: string;
    aud: string;
    sub: string;
    email: string;
    email_verified: boolean;
    at_hash: string;
    nonce: string;
    name: string;
    picture: string;
    given_name: string;
    family_name: string;
    iat: number;
    exp: number;
}

interface TikTokInfo extends Document {
    access_token: string;
    expires_in: number;
    open_id: string;
    refresh_expires_in: number;
    refresh_token: string;
    scope: string;
    token_type: string;
}

interface FacebookInfo extends Document {
    id: string;
    email: string;
}

interface TwitterInfo extends Document {
    id: number;
    id_str: string;
    name: string;
    screen_name: string;
    location: string;
    description: string;
    url: string | null;
    entities: {
        description: {
            urls: Array<any>;
        };
    };
    protected: boolean;
    followers_count: number;
    friends_count: number;
    listed_count: number;
    created_at: string;
    favourites_count: number;
    utc_offset: number | null;
    time_zone: string | null;
    geo_enabled: boolean;
    verified: boolean;
    statuses_count: number;
    lang: string | null;
    contributors_enabled: boolean;
    is_translator: boolean;
    is_translation_enabled: boolean;
    profile_background_color: string;
    profile_background_image_url: string | null;
    profile_background_image_url_https: string | null;
    profile_background_tile: boolean;
    profile_image_url: string;
    profile_image_url_https: string;
    profile_link_color: string;
    profile_sidebar_border_color: string;
    profile_sidebar_fill_color: string;
    profile_text_color: string;
    profile_use_background_image: boolean;
    has_extended_profile: boolean;
    default_profile: boolean;
    default_profile_image: boolean;
    following: boolean;
    follow_request_sent: boolean;
    notifications: boolean;
    translator_type: string;
    withheld_in_countries: Array<any>;
    suspended: boolean;
    needs_phone_verification: boolean;
    email: string;
}

const AppleInfoSchema = new Schema({
    iss: { type: String },
    aud: { type: String },
    exp: { type: Number },
    iat: { type: Number },
    sub: { type: String },
    c_hash: { type: String },
    email: { type: String },
    email_verified: { type: Boolean },
    is_private_email: { type: Boolean },
    auth_time: { type: Number },
    nonce_supported: { type: Boolean }
});

const GoogleInfoSchema = new Schema({
    iss: { type: String },
    azp: { type: String },
    aud: { type: String },
    sub: { type: String },
    email: { type: String },
    email_verified: { type: Boolean },
    at_hash: { type: String },
    nonce: { type: String },
    name: { type: String },
    picture: { type: String },
    given_name: { type: String },
    family_name: { type: String },
    iat: { type: Number },
    exp: { type: Number }
});

// 定义 TikTok 用户信息 Schema
const TikTokInfoSchema = new Schema({
    access_token: { type: String, required: true },
    expires_in: { type: Number, required: true },
    open_id: { type: String, required: true },
    refresh_expires_in: { type: Number, required: true },
    refresh_token: { type: String, required: true },
    scope: { type: String, required: true },
    token_type: { type: String, required: true }
});

const FacebookInfoSchema = new Schema({
    id: { type: String },
    email: { type: String }
});

const TwitterInfoSchema = new Schema({
    id: { type: Number, required: true },
    id_str: { type: String, required: true },
    name: { type: String, required: true },
    screen_name: { type: String, required: true },
    location: { type: String, default: '' },
    description: { type: String, default: '' },
    url: { type: String, default: null },
    entities: {
        description: {
            urls: [{ type: Schema.Types.Mixed }]
        }
    },
    protected: { type: Boolean, required: true },
    followers_count: { type: Number, required: true },
    friends_count: { type: Number, required: true },
    listed_count: { type: Number, required: true },
    created_at: { type: String, required: true },
    favourites_count: { type: Number, required: true },
    utc_offset: { type: Number, default: null },
    time_zone: { type: String, default: null },
    geo_enabled: { type: Boolean, required: true },
    verified: { type: Boolean, required: true },
    statuses_count: { type: Number, required: true },
    lang: { type: String, default: null },
    contributors_enabled: { type: Boolean, required: true },
    is_translator: { type: Boolean, required: true },
    is_translation_enabled: { type: Boolean, required: true },
    profile_background_color: { type: String, required: true },
    profile_background_image_url: { type: String, default: null },
    profile_background_image_url_https: { type: String, default: null },
    profile_background_tile: { type: Boolean, required: true },
    profile_image_url: { type: String, required: true },
    profile_image_url_https: { type: String, required: true },
    profile_link_color: { type: String, required: true },
    profile_sidebar_border_color: { type: String, required: true },
    profile_sidebar_fill_color: { type: String, required: true },
    profile_text_color: { type: String, required: true },
    profile_use_background_image: { type: Boolean, required: true },
    has_extended_profile: { type: Boolean, required: true },
    default_profile: { type: Boolean, required: true },
    default_profile_image: { type: Boolean, required: true },
    following: { type: Boolean, required: true },
    follow_request_sent: { type: Boolean, required: true },
    notifications: { type: Boolean, required: true },
    translator_type: { type: String, required: true },
    withheld_in_countries: [{ type: Schema.Types.Mixed }],
    suspended: { type: Boolean, required: true },
    needs_phone_verification: { type: Boolean, required: true },
    email: { type: String, required: true }
});


// 用户模型的TypeScript接口
export interface IUser extends Document {
    // userId?: Schema.Types.ObjectId; // 关联用户ID
    username?: string;
    password?: string;
    email: string;
    fullName?: string;
    birthDate?: Date;
    gender?: 'Male' | 'Female' | 'Other';
    phone?: string;
    address?: {
        street?: string;
        city?: string;
        state?: string;
        country?: string;
        postalCode?: string;
    };
    language?: string;
    timeZone?: string;
    occupation?: string;
    company?: string;
    allergies?: string[];
    medicalConditions?: string[];
    balance: number;
    // 管理员相关字段
    role?: 'user' | 'admin' | 'super_admin';
    status?: 'active' | 'inactive' | 'suspended';
    lastLoginAt?: Date;
    lastLoginIP?: string;
    avatar?: string;
    externalAccounts?: {
        weChatId?: string;
        qqId?: string;
        appleInfo?: AppleInfo;
        googleInfo?: GoogleInfo;
        facebookInfo?: FacebookInfo;
        twitterInfo?: TwitterInfo;
        tikTokInfo?: TikTokInfo;
    };
    isDelete: boolean;
}

const userSchema = new Schema({
    // userId: { type: Schema.Types.ObjectId, ref: 'User' }, // 关联用户ID
    username: { type: String },
    password: { type: String },
    email: { type: String, required: true, unique: true },
    fullName: { type: String },
    birthDate: { type: Date },
    gender: { type: String, enum: ['Male', 'Female', 'Other'] },
    phone: { type: String },
    address: {
        street: { type: String },
        city: { type: String },
        state: { type: String },
        country: { type: String },
        postalCode: { type: String }
    },
    language: { type: String, default: 'zh_CN' },
    timeZone: { type: String },
    occupation: { type: String },
    company: { type: String },
    allergies: [String],
    medicalConditions: [String],
    balance: { type: Number, default: defaultBalance },
    // 管理员相关字段
    role: { type: String, enum: ['user', 'admin', 'super_admin'], default: 'user' },
    status: { type: String, enum: ['active', 'inactive', 'suspended'], default: 'active' },
    lastLoginAt: { type: Date },
    lastLoginIP: { type: String },
    avatar: { type: String },
    isDelete: { type: Boolean, default: false },

    // 外部账户绑定
    externalAccounts: {
        // 使用sparse索引允许字段值唯一或null
        weChatId: { type: String, unique: true, sparse: true },
        qqId: { type: String, unique: true, sparse: true },
        appleInfo: { type: AppleInfoSchema },
        googleInfo: { type: GoogleInfoSchema },
        facebookInfo: { type: FacebookInfoSchema },
        twitterInfo: { type: TwitterInfoSchema },
        tikTokInfo: { type: TikTokInfoSchema }
    },
    hasPurchase: { type: Boolean, default: false},
},{
    versionKey: false,
    timestamps: true
});

// 创建虚拟属性 userId，作为 _id 的别名，确保返回字符串格式
userSchema.virtual('userId').get(function () {
    return this._id.toString();
});

// 确保虚拟属性包含在 JSON 和对象转换中
userSchema.set('toJSON', {
    virtuals: true,
    transform: function(doc, ret) {
        // 确保 _id 和 userId 都是字符串格式
        if (ret._id) {
            ret._id = ret._id.toString();
        }
        if (ret.userId) {
            ret.userId = ret.userId.toString();
        }
        return ret;
    }
});
userSchema.set('toObject', {
    virtuals: true,
    transform: function(doc, ret) {
        // 确保 _id 和 userId 都是字符串格式
        if (ret._id) {
            ret._id = ret._id.toString();
        }
        if (ret.userId) {
            ret.userId = ret.userId.toString();
        }
        return ret;
    }
});

userSchema.post<IUser>('save', async function () {
    try {
        await UserBalance.create({ userId: this._id, balance: defaultBalance });
    } catch (error) {
        console.error('Failed to insert default balance for user:', error);
        throw error;
    }
});

const modelName = 'User';

// 检查模型是否已经存在，如果存在则使用已存在的模型
const User: Model<IUser> = mongoose.models[modelName] || mongoose.model<IUser>(modelName, userSchema);

export default User;
