/**
 * 基础模型类
 * 提供通用的模型功能和类型定义
 */

import { Document, Schema, Model, Types } from 'mongoose';

/**
 * 基础文档接口
 */
export interface IBaseDocument extends Document {
    _id: Types.ObjectId;
    createdAt: Date;
    updatedAt: Date;
    isDeleted: boolean;
    version: number;
}

/**
 * 基础模型接口
 */
export interface IBaseModel<T extends IBaseDocument> extends Model<T> {
    findActive(): Promise<T[]>;
    findByIdActive(id: string): Promise<T | null>;
    softDelete(id: string): Promise<T | null>;
    restore(id: string): Promise<T | null>;
}

/**
 * 基础Schema选项
 */
export const baseSchemaOptions = {
    timestamps: true,
    versionKey: 'version',
    toJSON: {
        virtuals: true,
        transform: function(doc: any, ret: any) {
            ret.id = ret._id.toString();
            ret._id = ret._id.toString();
            delete ret.__v;
            return ret;
        }
    },
    toObject: {
        virtuals: true,
        transform: function(doc: any, ret: any) {
            ret.id = ret._id.toString();
            ret._id = ret._id.toString();
            delete ret.__v;
            return ret;
        }
    }
};

/**
 * 创建基础Schema
 */
export function createBaseSchema<T>(definition: any): Schema<T> {
    const schema = new Schema<T>({
        ...definition,
        isDeleted: {
            type: Boolean,
            default: false,
            index: true
        }
    }, baseSchemaOptions);

    // 添加虚拟属性
    schema.virtual('id').get(function() {
        return this._id.toString();
    });

    // 添加静态方法
    schema.statics.findActive = function() {
        return this.find({ isDeleted: false });
    };

    schema.statics.findByIdActive = function(id: string) {
        return this.findOne({ _id: id, isDeleted: false });
    };

    schema.statics.softDelete = function(id: string) {
        return this.findByIdAndUpdate(
            id,
            { isDeleted: true },
            { new: true }
        );
    };

    schema.statics.restore = function(id: string) {
        return this.findByIdAndUpdate(
            id,
            { isDeleted: false },
            { new: true }
        );
    };

    // 添加实例方法
    schema.methods.softDelete = function() {
        this.isDeleted = true;
        return this.save();
    };

    schema.methods.restore = function() {
        this.isDeleted = false;
        return this.save();
    };

    // 添加中间件
    schema.pre('find', function() {
        this.where({ isDeleted: false });
    });

    schema.pre('findOne', function() {
        this.where({ isDeleted: false });
    });

    schema.pre('findOneAndUpdate', function() {
        this.where({ isDeleted: false });
    });

    schema.pre('count', function() {
        this.where({ isDeleted: false });
    });

    schema.pre('countDocuments', function() {
        this.where({ isDeleted: false });
    });

    return schema;
}

/**
 * 通用验证器
 */
export const validators = {
    email: {
        validator: function(email: string) {
            return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        },
        message: 'Invalid email format'
    },
    
    phone: {
        validator: function(phone: string) {
            return /^[\+]?[1-9][\d]{0,15}$/.test(phone);
        },
        message: 'Invalid phone number format'
    },
    
    url: {
        validator: function(url: string) {
            try {
                new URL(url);
                return true;
            } catch {
                return false;
            }
        },
        message: 'Invalid URL format'
    },
    
    positiveNumber: {
        validator: function(value: number) {
            return value >= 0;
        },
        message: 'Value must be non-negative'
    }
};

/**
 * 通用Schema字段定义
 */
export const commonFields = {
    email: {
        type: String,
        required: true,
        lowercase: true,
        trim: true,
        validate: validators.email,
        index: true
    },
    
    phone: {
        type: String,
        trim: true,
        validate: validators.phone
    },
    
    url: {
        type: String,
        trim: true,
        validate: validators.url
    },
    
    positiveNumber: {
        type: Number,
        min: 0,
        validate: validators.positiveNumber
    },
    
    language: {
        type: String,
        enum: ['zh_CN', 'en_US', 'ja_JP', 'ko_KR'],
        default: 'zh_CN'
    },
    
    currency: {
        type: String,
        enum: ['CNY', 'USD', 'EUR', 'JPY', 'KRW'],
        default: 'CNY'
    }
};

export default createBaseSchema;
