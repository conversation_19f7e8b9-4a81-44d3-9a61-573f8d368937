/**
 * 数据库连接管理器
 * 提供统一的数据库连接、断开连接和健康检查功能
 */

import mongoose, { Connection } from 'mongoose';
import { logger } from '../../utils/logger';

export interface DatabaseConfig {
    uri: string;
    options?: mongoose.ConnectOptions;
    retryAttempts?: number;
    retryDelay?: number;
}

export class DatabaseManager {
    private static instance: DatabaseManager;
    private connection: Connection | null = null;
    private config: DatabaseConfig;
    private isConnecting = false;
    private reconnectTimer: NodeJS.Timeout | null = null;

    private constructor(config: DatabaseConfig) {
        this.config = {
            retryAttempts: 5,
            retryDelay: 5000,
            options: {
                maxPoolSize: 10,
                serverSelectionTimeoutMS: 5000,
                socketTimeoutMS: 45000,
                bufferCommands: false,
                bufferMaxEntries: 0,
            },
            ...config
        };
    }

    /**
     * 获取数据库管理器实例
     */
    public static getInstance(config?: DatabaseConfig): DatabaseManager {
        if (!DatabaseManager.instance) {
            if (!config) {
                throw new Error('DatabaseManager requires config for first initialization');
            }
            DatabaseManager.instance = new DatabaseManager(config);
        }
        return DatabaseManager.instance;
    }

    /**
     * 连接数据库
     */
    public async connect(): Promise<void> {
        if (this.connection?.readyState === 1) {
            logger.info('Database already connected');
            return;
        }

        if (this.isConnecting) {
            logger.info('Database connection in progress');
            return;
        }

        this.isConnecting = true;

        try {
            logger.info('Connecting to database...');
            
            await mongoose.connect(this.config.uri, this.config.options);
            
            this.connection = mongoose.connection;
            this.setupEventListeners();
            
            logger.info('Database connected successfully');
            this.isConnecting = false;
            
        } catch (error) {
            this.isConnecting = false;
            logger.error('Database connection failed:', error);
            throw error;
        }
    }

    /**
     * 断开数据库连接
     */
    public async disconnect(): Promise<void> {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        if (this.connection) {
            await mongoose.disconnect();
            this.connection = null;
            logger.info('Database disconnected');
        }
    }

    /**
     * 获取连接状态
     */
    public getConnectionState(): string {
        const states = {
            0: 'disconnected',
            1: 'connected',
            2: 'connecting',
            3: 'disconnecting'
        };
        return states[this.connection?.readyState || 0] || 'unknown';
    }

    /**
     * 健康检查
     */
    public async healthCheck(): Promise<boolean> {
        try {
            if (!this.connection || this.connection.readyState !== 1) {
                return false;
            }
            
            // 执行简单的ping操作
            await this.connection.db.admin().ping();
            return true;
        } catch (error) {
            logger.error('Database health check failed:', error);
            return false;
        }
    }

    /**
     * 设置事件监听器
     */
    private setupEventListeners(): void {
        if (!this.connection) return;

        this.connection.on('connected', () => {
            logger.info('Database connected');
        });

        this.connection.on('error', (error) => {
            logger.error('Database connection error:', error);
        });

        this.connection.on('disconnected', () => {
            logger.warn('Database disconnected');
            this.scheduleReconnect();
        });

        this.connection.on('reconnected', () => {
            logger.info('Database reconnected');
        });

        // 优雅关闭
        process.on('SIGINT', async () => {
            await this.disconnect();
            process.exit(0);
        });

        process.on('SIGTERM', async () => {
            await this.disconnect();
            process.exit(0);
        });
    }

    /**
     * 计划重连
     */
    private scheduleReconnect(): void {
        if (this.reconnectTimer) return;

        this.reconnectTimer = setTimeout(async () => {
            this.reconnectTimer = null;
            try {
                await this.connect();
            } catch (error) {
                logger.error('Reconnection failed:', error);
                this.scheduleReconnect();
            }
        }, this.config.retryDelay);
    }

    /**
     * 获取数据库连接
     */
    public getConnection(): Connection | null {
        return this.connection;
    }

    /**
     * 获取数据库实例
     */
    public getDatabase() {
        return this.connection?.db;
    }
}

export default DatabaseManager;
