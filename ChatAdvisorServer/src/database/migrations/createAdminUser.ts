/**
 * 创建默认管理员用户的数据库迁移脚本
 */

import bcrypt from 'bcryptjs';
import User from '../../models/User';
import { logger } from '../../utils/logger';

/**
 * 创建默认管理员用户
 */
export async function createDefaultAdminUser(): Promise<void> {
    try {
        // 检查是否已存在管理员用户
        const existingAdmin = await User.findOne({
            role: { $in: ['admin', 'super_admin'] },
            isDelete: { $ne: true }
        });

        if (existingAdmin) {
            logger.info('Admin user already exists, skipping creation');
            return;
        }

        // 创建默认超级管理员
        const adminPassword = process.env.ADMIN_PASSWORD || 'admin123456';
        const hashedPassword = await bcrypt.hash(adminPassword, 12);

        const adminUser = new User({
            email: '<EMAIL>',
            password: hashedPassword,
            fullName: '系统管理员',
            role: 'super_admin',
            status: 'active',
            username: 'admin',
            language: 'zh_CN',
            balance: 0,
            isDelete: false
        });

        await adminUser.save();
        logger.info('Default admin user created successfully');
        logger.info(`Admin email: <EMAIL>`);
        logger.info(`Admin password: ${adminPassword}`);

        // 创建普通管理员（可选）
        const moderatorPassword = process.env.MODERATOR_PASSWORD || 'mod123456';
        const hashedModPassword = await bcrypt.hash(moderatorPassword, 12);

        const moderatorUser = new User({
            email: '<EMAIL>',
            password: hashedModPassword,
            fullName: '内容管理员',
            role: 'admin',
            status: 'active',
            username: 'moderator',
            language: 'zh_CN',
            balance: 0,
            isDelete: false
        });

        await moderatorUser.save();
        logger.info('Default moderator user created successfully');
        logger.info(`Moderator email: <EMAIL>`);
        logger.info(`Moderator password: ${moderatorPassword}`);

    } catch (error) {
        logger.error('Failed to create default admin user:', error);
        throw error;
    }
}

/**
 * 更新现有用户为管理员（如果需要）
 */
export async function promoteUserToAdmin(email: string, role: 'admin' | 'super_admin' = 'admin'): Promise<void> {
    try {
        const user = await User.findOne({ email, isDelete: { $ne: true } });
        
        if (!user) {
            throw new Error(`User with email ${email} not found`);
        }

        user.role = role;
        user.status = 'active';
        await user.save();

        logger.info(`User ${email} promoted to ${role}`);
    } catch (error) {
        logger.error(`Failed to promote user ${email} to admin:`, error);
        throw error;
    }
}

/**
 * 重置管理员密码
 */
export async function resetAdminPassword(email: string, newPassword: string): Promise<void> {
    try {
        const user = await User.findOne({ 
            email, 
            role: { $in: ['admin', 'super_admin'] },
            isDelete: { $ne: true }
        });
        
        if (!user) {
            throw new Error(`Admin user with email ${email} not found`);
        }

        const hashedPassword = await bcrypt.hash(newPassword, 12);
        user.password = hashedPassword;
        await user.save();

        logger.info(`Password reset for admin user ${email}`);
    } catch (error) {
        logger.error(`Failed to reset password for admin ${email}:`, error);
        throw error;
    }
}
