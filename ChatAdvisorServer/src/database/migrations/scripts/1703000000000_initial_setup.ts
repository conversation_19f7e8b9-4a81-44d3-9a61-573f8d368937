/**
 * Migration: Initial Setup
 * Version: 1703000000000
 * Description: Initial database setup and data migration from old schema
 */

import { IMigrationScript } from '../MigrationManager';
import { User } from '../../models/User';
import { logger } from '../../../utils/logger';

const migration: IMigrationScript = {
    name: 'Initial Setup',
    version: 1703000000000,
    description: 'Initial database setup and data migration from old schema',

    async up(): Promise<void> {
        logger.info('Running initial setup migration...');

        try {
            // 1. 确保用户集合存在
            const collections = await User.db.listCollections().toArray();
            const userCollectionExists = collections.some(col => col.name === 'users');

            if (!userCollectionExists) {
                logger.info('Creating users collection...');
                await User.db.createCollection('users');
            }

            // 2. 迁移现有用户数据（如果有的话）
            const existingUsers = await User.find({}).lean();
            logger.info(`Found ${existingUsers.length} existing users`);

            // 3. 更新用户数据结构
            for (const user of existingUsers) {
                const updateData: any = {};

                // 确保必需字段存在
                if (!user.language) {
                    updateData.language = 'zh_CN';
                }

                if (!user.currency) {
                    updateData.currency = 'CNY';
                }

                if (user.balance === undefined || user.balance === null) {
                    updateData.balance = 0;
                }

                if (user.totalSpent === undefined || user.totalSpent === null) {
                    updateData.totalSpent = 0;
                }

                if (user.loginCount === undefined || user.loginCount === null) {
                    updateData.loginCount = 0;
                }

                if (user.chatCount === undefined || user.chatCount === null) {
                    updateData.chatCount = 0;
                }

                if (user.emailVerified === undefined || user.emailVerified === null) {
                    updateData.emailVerified = false;
                }

                if (user.phoneVerified === undefined || user.phoneVerified === null) {
                    updateData.phoneVerified = false;
                }

                if (user.isVip === undefined || user.isVip === null) {
                    updateData.isVip = false;
                }

                if (user.hasPurchase === undefined || user.hasPurchase === null) {
                    updateData.hasPurchase = false;
                }

                // 如果有更新数据，则执行更新
                if (Object.keys(updateData).length > 0) {
                    await User.updateOne({ _id: user._id }, { $set: updateData });
                    logger.info(`Updated user ${user.email} with new fields`);
                }
            }

            // 4. 创建默认管理员用户（如果不存在）
            const adminEmail = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>';
            const existingAdmin = await User.findOne({ email: adminEmail });

            if (!existingAdmin) {
                const adminUser = new User({
                    email: adminEmail,
                    username: 'admin',
                    fullName: 'System Administrator',
                    language: 'zh_CN',
                    currency: 'CNY',
                    balance: 1000000, // 给管理员账户充足余额
                    emailVerified: true,
                    isVip: true
                });

                await adminUser.save();
                logger.info(`Created default admin user: ${adminEmail}`);
            }

            logger.info('Initial setup migration completed successfully');

        } catch (error) {
            logger.error('Initial setup migration failed:', error);
            throw error;
        }
    },

    async down(): Promise<void> {
        logger.info('Rolling back initial setup migration...');

        try {
            // 回滚操作：移除新添加的字段
            await User.updateMany({}, {
                $unset: {
                    currency: '',
                    totalSpent: '',
                    loginCount: '',
                    chatCount: '',
                    emailVerified: '',
                    phoneVerified: '',
                    isVip: '',
                    vipExpiredAt: ''
                }
            });

            // 删除默认管理员用户
            const adminEmail = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>';
            await User.deleteOne({ email: adminEmail });

            logger.info('Initial setup migration rolled back successfully');

        } catch (error) {
            logger.error('Initial setup migration rollback failed:', error);
            throw error;
        }
    }
};

export default migration;
