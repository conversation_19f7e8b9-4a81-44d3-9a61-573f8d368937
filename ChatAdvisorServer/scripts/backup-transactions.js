/**
 * 交易记录备份脚本
 * 在修复数据之前创建备份
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// 连接数据库
const connectDB = async () => {
    try {
        const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/chatadvisor';
        await mongoose.connect(mongoURI);
        console.log('✅ 数据库连接成功');
    } catch (error) {
        console.error('❌ 数据库连接失败:', error);
        process.exit(1);
    }
};

// 定义 BalanceTransaction 模型
const BalanceTransactionSchema = new mongoose.Schema({
    userId: { type: String, required: true },
    amount: { type: Number, required: true },
    reason: { type: String, required: true },
    timestamp: { type: Date, default: Date.now },
    modelId: { type: mongoose.Schema.Types.ObjectId, ref: 'Pricing' },
    type: { type: Number, required: true }
}, {
    versionKey: false
});

const BalanceTransaction = mongoose.model('BalanceTransaction', BalanceTransactionSchema);

// 创建备份
const createBackup = async () => {
    try {
        console.log('📦 开始创建交易记录备份...');

        // 获取所有交易记录
        const transactions = await BalanceTransaction.find({}).lean();
        
        console.log(`📊 找到 ${transactions.length} 笔交易记录`);

        // 创建备份目录
        const backupDir = path.join(__dirname, '../backups');
        if (!fs.existsSync(backupDir)) {
            fs.mkdirSync(backupDir, { recursive: true });
        }

        // 生成备份文件名
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupFile = path.join(backupDir, `balance_transactions_backup_${timestamp}.json`);

        // 创建备份元数据
        const backupData = {
            metadata: {
                createdAt: new Date(),
                totalRecords: transactions.length,
                description: '交易记录备份 - 修复交易类型之前',
                version: '1.0'
            },
            transactions: transactions
        };

        // 写入备份文件
        fs.writeFileSync(backupFile, JSON.stringify(backupData, null, 2));

        console.log(`✅ 备份创建成功: ${backupFile}`);
        console.log(`📁 备份文件大小: ${(fs.statSync(backupFile).size / 1024 / 1024).toFixed(2)} MB`);

        return backupFile;

    } catch (error) {
        console.error('❌ 创建备份失败:', error);
        throw error;
    }
};

// 验证备份
const verifyBackup = async (backupFile) => {
    try {
        console.log('🔍 验证备份文件...');

        const backupData = JSON.parse(fs.readFileSync(backupFile, 'utf8'));
        
        console.log(`📊 备份验证结果:`);
        console.log(`  - 记录数量: ${backupData.transactions.length}`);
        console.log(`  - 创建时间: ${backupData.metadata.createdAt}`);
        console.log(`  - 文件完整性: ✅`);

        return true;
    } catch (error) {
        console.error('❌ 备份验证失败:', error);
        return false;
    }
};

// 主函数
const main = async () => {
    try {
        await connectDB();
        
        const backupFile = await createBackup();
        const isValid = await verifyBackup(backupFile);
        
        if (isValid) {
            console.log('\n🎉 备份创建并验证成功!');
            console.log(`📁 备份文件: ${backupFile}`);
            console.log('\n💡 现在可以安全地运行修复脚本了');
        } else {
            console.log('\n❌ 备份验证失败，请检查备份文件');
        }

    } catch (error) {
        console.error('❌ 备份脚本执行失败:', error);
    } finally {
        await mongoose.disconnect();
        console.log('📴 数据库连接已关闭');
        process.exit(0);
    }
};

// 运行脚本
if (require.main === module) {
    main();
}

module.exports = {
    createBackup,
    verifyBackup
};
