#!/bin/bash

# 快速部署脚本
# 一键部署到生产环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查部署依赖..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    # 检查 PM2
    if ! command -v pm2 &> /dev/null; then
        log_warning "PM2 未安装，将使用 npm start"
    fi
    
    log_success "依赖检查完成"
}

# 准备生产环境配置
prepare_config() {
    log_info "准备生产环境配置..."
    
    # 如果没有生产环境配置，创建模板
    if [[ ! -f ".env.production" ]]; then
        log_info "创建生产环境配置模板..."
        ./scripts/deploy-config.sh --template
        
        log_warning "请编辑 .env.production.template 文件，然后重命名为 .env.production"
        log_warning "或者手动创建 .env.production 文件"
        
        read -p "是否现在编辑配置文件？(y/n): " edit_config
        if [[ "$edit_config" == "y" || "$edit_config" == "Y" ]]; then
            ${EDITOR:-nano} .env.production.template
            mv .env.production.template .env.production
        else
            log_error "请先配置 .env.production 文件"
            exit 1
        fi
    fi
    
    # 验证配置
    log_info "验证生产环境配置..."
    if ! ./scripts/deploy-config.sh --validate .env.production; then
        log_error "配置验证失败"
        exit 1
    fi
    
    log_success "配置准备完成"
}

# 构建应用
build_app() {
    log_info "构建应用..."
    
    # 安装依赖
    log_info "安装依赖..."
    npm install --production
    
    # 构建 TypeScript
    log_info "编译 TypeScript..."
    npm run build
    
    log_success "应用构建完成"
}

# 部署配置
deploy_config() {
    log_info "部署配置..."
    
    # 备份现有配置
    if [[ -f ".env" ]]; then
        ./scripts/deploy-config.sh --backup
    fi
    
    # 部署生产环境配置
    ./scripts/deploy-config.sh --env .env.production
    
    log_success "配置部署完成"
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    # 停止现有服务
    if command -v pm2 &> /dev/null; then
        pm2 stop chat-advisor-release 2>/dev/null || true
        pm2 delete chat-advisor-release 2>/dev/null || true
        
        # 使用 PM2 启动
        npm run pm-release
        
        log_success "服务已通过 PM2 启动"
        log_info "使用 'pm2 status' 查看服务状态"
        log_info "使用 'pm2 logs chat-advisor-release' 查看日志"
    else
        # 使用 npm 启动
        log_warning "PM2 未安装，使用 npm 启动服务"
        log_info "服务将在前台运行，按 Ctrl+C 停止"
        npm run release
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 等待服务启动
    sleep 5
    
    # 检查服务是否运行
    local port=$(grep "^PORT=" .env | cut -d'=' -f2)
    port=${port:-53011}
    
    if curl -s "http://localhost:$port/health" > /dev/null; then
        log_success "服务运行正常，端口: $port"
    else
        log_warning "无法连接到服务，请检查日志"
    fi
    
    # 验证 xAI 配置
    log_info "验证 xAI 配置..."
    if npm run verify-config; then
        log_success "xAI 配置验证通过"
    else
        log_warning "xAI 配置验证失败，请检查配置"
    fi
}

# 显示部署信息
show_deployment_info() {
    local port=$(grep "^PORT=" .env | cut -d'=' -f2)
    port=${port:-53011}
    
    echo ""
    log_success "🎉 部署完成！"
    echo ""
    echo "服务信息:"
    echo "  - 端口: $port"
    echo "  - 环境: production"
    echo "  - API: xAI (Grok)"
    echo ""
    echo "管理命令:"
    if command -v pm2 &> /dev/null; then
        echo "  - 查看状态: pm2 status"
        echo "  - 查看日志: pm2 logs chat-advisor-release"
        echo "  - 重启服务: pm2 restart chat-advisor-release"
        echo "  - 停止服务: pm2 stop chat-advisor-release"
    else
        echo "  - 停止服务: Ctrl+C"
    fi
    echo ""
    echo "访问地址:"
    echo "  - 本地: http://localhost:$port"
    echo "  - 健康检查: http://localhost:$port/health"
    echo ""
}

# 主函数
main() {
    echo "🚀 ChatAdvisor 生产环境快速部署"
    echo "=================================="
    echo ""
    
    # 检查是否在正确的目录
    if [[ ! -f "package.json" ]]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 确认部署
    read -p "确认部署到生产环境？(y/n): " confirm
    if [[ "$confirm" != "y" && "$confirm" != "Y" ]]; then
        log_info "部署已取消"
        exit 0
    fi
    
    # 执行部署步骤
    check_dependencies
    prepare_config
    build_app
    deploy_config
    start_service
    verify_deployment
    show_deployment_info
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 运行主函数
main "$@"
