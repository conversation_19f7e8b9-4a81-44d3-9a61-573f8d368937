# ChatAdvisor Server GitLab CI/CD 配置
# 当提交信息包含 "release:" 时自动触发后端部署
# 使用本地PM2部署，无需SSH连接

variables:
  BACKEND_PORT: "53011"
  SERVICE_NAME: "chat-advisor-release"
  NODE_VERSION: "20"
  NPM_CACHE_DIR: ".npm"

cache:
  key: backend-${CI_COMMIT_REF_SLUG}
  paths:
    - .npm/
    - node_modules/

stages:
  - validate
  - build
  - deploy
  - test
  - notify

workflow:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /release:/
      when: always
    - if: $CI_PIPELINE_SOURCE == "web"
      when: always
    - when: never

validate_backend:
  stage: validate
  tags:
    - Eva
  script:
    - "echo '🔍 验证后端部署环境...'"
    - "echo '提交信息: $CI_COMMIT_MESSAGE'"
    - "echo '分支: $CI_COMMIT_REF_NAME'"
    - "echo '项目: ChatAdvisor Server'"
    - "node --version"
    - "npm --version"
    - "echo '✅ 后端环境验证完成'"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

build_backend:
  stage: build
  tags:
    - Eva
  script:
    - "echo '🏗️ 构建后端项目...'"
    - "npm ci --cache .npm --prefer-offline"
    - "echo '🔍 环境配置检查...'"
    - |
      if [ -f ".env" ]; then
        echo "✅ 发现环境配置文件"
        # 检查关键配置项（不显示敏感信息）
        grep -q "OPENAI_API_KEY" .env && echo "✅ API密钥配置存在"
        grep -q "MONGODB_URI" .env && echo "✅ 数据库配置存在"
        grep -q "JWT_SECRET" .env && echo "✅ JWT密钥配置存在"
      else
        echo "⚠️ 未找到环境配置文件"
      fi
    - "echo '🏗️ TypeScript编译...'"
    - "npm run build"
    - "echo '📦 构建产物信息:'"
    - "du -sh dist/"
    - "find dist -name '*.js' | wc -l | xargs echo 'JavaScript文件数量:'"
    - "echo '✅ 后端构建完成'"
  artifacts:
    name: "backend-$CI_COMMIT_SHORT_SHA"
    paths:
      - dist/
      - package.json
      - pm2.config.cjs
      - .env
    expire_in: 1 hour
  cache:
    key: backend-${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
      - .npm/
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

test_backend:
  stage: build
  tags:
    - Eva
  script:
    - "echo '🧪 运行后端测试...'"
    - "npm ci --cache .npm --prefer-offline"
    - |
      # 运行测试（如果存在）
      if npm run | grep -q "test"; then
        echo "🔍 执行单元测试..."
        npm test || echo "⚠️ 测试失败，但继续构建流程"
      else
        echo "ℹ️ 未找到测试脚本，跳过测试"
      fi
    - "echo '✅ 测试阶段完成'"
  cache:
    key: backend-${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
      - .npm/
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

deploy_backend:
  stage: deploy
  tags:
    - Eva
  dependencies:
    - build_backend
  before_script:
    - "echo '🚀 准备部署后端到生产环境...'"
    - "echo '📦 验证构建产物...'"
    - "ls -la dist/"
    - "du -sh dist/"
  script:
    - "echo '🔄 部署后端构建产物...'"
    - |
      set -e

      # 备份当前版本（如果存在）
      if [ -d "dist.current" ]; then
        echo "📦 备份当前版本..."
        mv dist.current dist.backup.$(date +%Y%m%d_%H%M%S)
        # 保留最近3个备份
        ls -dt dist.backup.* 2>/dev/null | tail -n +4 | xargs rm -rf || true
      fi

      # 移动新构建产物到当前版本
      echo "📂 部署新的构建产物..."
      mv dist dist.current

      echo "🔍 验证构建产物..."
      if [ ! -d "dist.current" ] || [ ! -f "dist.current/src/index.js" ]; then
        echo "❌ 构建产物验证失败"
        exit 1
      fi

      echo "📊 构建产物信息:"
      du -sh dist.current/
      ls -la dist.current/

      echo "🔄 使用PM2重启后端服务..."

      # 停止现有服务
      pm2 stop $SERVICE_NAME 2>/dev/null || echo "后端服务未运行"

      # 启动服务
      pm2 start pm2.config.cjs --only $SERVICE_NAME

      # 保存PM2配置
      pm2 save

      echo "⏳ 等待服务启动..."
      sleep 10

      echo "📊 检查PM2服务状态..."
      pm2 status $SERVICE_NAME

      echo "✅ 后端部署完成"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

health_check_backend:
  stage: test
  tags:
    - Eva
  dependencies:
    - deploy_backend
  script:
    - "echo '🏥 执行后端健康检查...'"
    - |
      echo "检查后端服务 (端口 $BACKEND_PORT)..."
      for i in {1..15}; do
        if curl -f -s "http://localhost:$BACKEND_PORT/health" > /dev/null; then
          echo "✅ 后端服务运行正常"
          break
        elif curl -f -s "http://localhost:$BACKEND_PORT" > /dev/null; then
          echo "✅ 后端服务运行正常（根路径）"
          break
        else
          echo "⏳ 等待后端服务启动... ($i/15)"
          sleep 4
        fi

        if [ $i -eq 15 ]; then
          echo "❌ 后端服务健康检查失败"
          exit 1
        fi
      done

      # 检查API响应
      echo "🔍 检查API响应..."
      api_response=$(curl -s "http://localhost:$BACKEND_PORT/health" 2>/dev/null || echo "")
      if [ -n "$api_response" ]; then
        echo "✅ API响应正常: $api_response"
      else
        echo "⚠️ 无法获取API响应"
      fi

      # 检查响应时间
      echo "⏱️ 检查响应时间..."
      response_time=$(curl -o /dev/null -s -w "%{time_total}" "http://localhost:$BACKEND_PORT/health" 2>/dev/null || echo "0")
      echo "后端响应时间: ${response_time}秒"

      echo "🎉 后端健康检查完成"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

database_check:
  stage: test
  tags:
    - Eva
  dependencies:
    - health_check_backend
  script:
    - "echo '🗄️ 检查数据库连接...'"
    - |
      # 通过API检查数据库状态
      db_check_url="http://localhost:$BACKEND_PORT/api/health/db"

      if curl -f -s "$db_check_url" > /dev/null 2>&1; then
        echo "✅ 数据库连接正常"
      else
        echo "⚠️ 无法通过API检查数据库状态"
        echo "ℹ️ 这可能是正常的，如果没有专门的数据库健康检查端点"
      fi

      echo "✅ 数据库检查完成"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

pm2_status_check:
  stage: test
  tags:
    - Eva
  dependencies:
    - database_check
  script:
    - "echo '🔍 检查PM2服务状态...'"
    - |
      echo "📊 PM2服务状态:"
      pm2 status

      echo ""
      echo "📋 后端服务详细信息:"
      pm2 show $SERVICE_NAME || echo "⚠️ 无法获取后端服务详细信息"

      echo ""
      echo "📝 后端服务日志 (最近10行):"
      pm2 logs $SERVICE_NAME --lines 10 --nostream || echo "⚠️ 无法获取后端服务日志"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

notify_success:
  stage: notify
  tags:
    - Eva
  dependencies:
    - pm2_status_check
  script:
    - "echo '🎉 后端部署成功通知'"
    - |
      echo "后端部署信息:"
      echo "- 项目: ChatAdvisor Server"
      echo "- 提交: $CI_COMMIT_SHORT_SHA"
      echo "- 分支: $CI_COMMIT_REF_NAME"
      echo "- 服务: $SERVICE_NAME"
      echo "- 访问地址: http://localhost:$BACKEND_PORT"
      echo "- 健康检查: http://localhost:$BACKEND_PORT/health"
      echo "- 流水线: $CI_PIPELINE_URL"
      echo ""
      echo "PM2管理命令:"
      echo "- 查看状态: pm2 status"
      echo "- 查看日志: pm2 logs $SERVICE_NAME"
      echo "- 重启服务: pm2 restart $SERVICE_NAME"
  when: on_success
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

notify_failure:
  stage: notify
  tags:
    - Eva
  script:
    - "echo '❌ 后端部署失败通知'"
    - |
      echo "后端部署失败信息:"
      echo "- 项目: ChatAdvisor Server"
      echo "- 提交: $CI_COMMIT_SHORT_SHA"
      echo "- 分支: $CI_COMMIT_REF_NAME"
      echo "- 流水线: $CI_PIPELINE_URL"
      echo ""
      echo "请检查:"
      echo "1. 构建日志中的错误信息"
      echo "2. TypeScript编译错误"
      echo "3. 环境配置文件是否正确"
      echo "4. 数据库连接是否正常"
      echo "5. PM2服务的运行状态"
      echo "6. 后端服务的日志输出"
      echo "7. 本地环境的配置状态"
  when: on_failure
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/
