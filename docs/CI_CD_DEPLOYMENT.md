# ChatAdvisor GitLab CI/CD 自动化部署指南

## 概述

本文档介绍如何为ChatAdvisor项目配置和使用GitLab CI/CD自动化部署系统。当Git提交信息包含"release:"关键字时，系统将自动触发构建和部署流程。

## 系统架构

### 部署流程图

```
Git提交(包含"release:") → GitLab CI/CD → 构建 → 部署 → 测试验证 → 通知
```

### 组件说明

- **GitLab Runner**: `#11 (4JJbezW6) eva` - 专用执行器
- **目标主机**: `**************` - 部署服务器
- **前端服务**: 端口 `34001` (生产环境)
- **后端服务**: 端口 `53011` (生产环境)

## 快速开始

### 1. 环境准备

#### GitLab Runner配置

确保GitLab Runner `eva` 已正确配置：

```bash
# 检查Runner状态
gitlab-runner status

# 验证Runner注册
gitlab-runner list
```

#### 目标服务器环境

目标服务器需要安装以下软件：

```bash
# Node.js 20+
node --version

# npm
npm --version

# PM2
pm2 --version

# Git
git --version
```

### 2. SSH密钥配置

在GitLab项目的CI/CD设置中配置以下变量：

| 变量名 | 类型 | 描述 |
|--------|------|------|
| `SSH_PRIVATE_KEY` | File | SSH私钥内容 |
| `DEPLOY_HOST` | Variable | 部署主机IP (默认: **************) |
| `DEPLOY_USER` | Variable | 部署用户 (默认: root) |
| `DEPLOY_PATH` | Variable | 部署路径 (默认: /opt/chatadvisor) |

#### SSH密钥生成

```bash
# 生成SSH密钥对
ssh-keygen -t rsa -b 4096 -C "gitlab-ci@chatadvisor"

# 将公钥添加到目标服务器
ssh-copy-id -i ~/.ssh/id_rsa.pub root@**************

# 将私钥内容复制到GitLab CI/CD变量
cat ~/.ssh/id_rsa
```

### 3. 触发部署

要触发自动部署，提交信息必须包含 `release:` 关键字：

```bash
# 示例提交信息
git commit -m "release: 更新用户界面和API接口"
git commit -m "release: v1.2.0 - 新增聊天功能"
git commit -m "feat: 添加新功能 release: 部署到生产环境"
```

## 配置文件说明

### .gitlab-ci.yml

主要配置文件，定义了完整的CI/CD流水线：

```yaml
# 流水线阶段
stages:
  - validate    # 环境验证
  - build      # 构建阶段
  - deploy     # 部署阶段
  - test       # 测试验证
  - notify     # 结果通知

# 触发条件
workflow:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /release:/
      when: always
    - when: never
```

### 部署脚本

#### ci/scripts/deploy.sh
主部署脚本，负责：
- SSH连接配置
- 代码更新
- 依赖安装
- 项目构建
- 服务重启
- 健康检查

#### ci/scripts/build-frontend.sh
前端构建脚本，包含：
- 依赖安装
- 代码质量检查
- TypeScript编译
- Vite构建
- 构建验证

#### ci/scripts/build-backend.sh
后端构建脚本，包含：
- 依赖安装
- 环境配置检查
- TypeScript编译
- 基础测试
- 构建验证

#### ci/scripts/health-check.sh
健康检查脚本，验证：
- 服务端口可访问性
- HTTP响应状态
- API功能正常
- 性能指标

## Playwright自动化测试

### 测试配置

测试配置文件：`ci/tests/playwright.config.js`

```javascript
module.exports = defineConfig({
  testDir: './tests',
  timeout: 30 * 1000,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  
  use: {
    baseURL: 'http://**************:34001',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
});
```

### 测试用例

#### GitLab流水线验证
- 访问GitLab流水线页面
- 检查最新流水线状态
- 验证部署结果

#### 服务功能验证
- 前端服务可访问性
- 后端API可访问性
- 前后端通信测试
- 性能和稳定性测试

### 运行测试

```bash
# 安装依赖
cd ci/tests
npm install

# 安装浏览器
npx playwright install

# 运行测试
npm test

# 查看报告
npm run test:report
```

## 部署流程详解

### 1. 验证阶段 (validate)

```bash
# 检查环境变量
echo "提交信息: $CI_COMMIT_MESSAGE"
echo "分支: $CI_COMMIT_REF_NAME"

# 验证Node.js环境
node --version
npm --version
```

### 2. 构建阶段 (build)

并行执行前端和后端构建：

**前端构建**：
```bash
cd admin-frontend
npm ci --cache .npm --prefer-offline
npm run lint
npm run build:prod
```

**后端构建**：
```bash
cd ChatAdvisorServer
npm ci --cache .npm --prefer-offline
npm run build
```

### 3. 部署阶段 (deploy)

```bash
# SSH连接到目标服务器
ssh root@**************

# 更新代码
git fetch origin
git reset --hard origin/main

# 安装依赖并构建
npm install --production
npm run build

# 重启服务
pm2 restart admin-frontend-release
pm2 restart chat-advisor-release
```

### 4. 测试阶段 (test)

```bash
# 健康检查
curl -f http://**************:34001
curl -f http://**************:53011/health

# Playwright自动化测试
npx playwright test
```

### 5. 通知阶段 (notify)

根据部署结果发送通知：
- 成功：显示访问地址和部署信息
- 失败：显示错误信息和流水线链接

## 监控和日志

### GitLab流水线监控

访问流水线页面查看执行状态：
```
https://gitlab.zweiteng.tk/server/admin-frontend/-/pipelines
```

### 服务器日志

PM2日志位置：
```bash
# 查看前端日志
pm2 logs admin-frontend-release

# 查看后端日志
pm2 logs chat-advisor-release

# 查看所有日志
pm2 logs
```

### 构建产物

构建产物保存位置：
- 前端：`admin-frontend/dist/`
- 后端：`ChatAdvisorServer/dist/`
- 测试报告：`ci/tests/playwright-report/`

## 故障排除

### 常见问题

#### 1. SSH连接失败

**症状**：部署阶段SSH连接超时或拒绝连接

**解决方案**：
```bash
# 检查SSH密钥配置
ssh -i ~/.ssh/id_rsa root@**************

# 验证目标服务器SSH服务
systemctl status ssh

# 检查防火墙设置
ufw status
```

#### 2. 服务启动失败

**症状**：PM2服务无法启动或立即退出

**解决方案**：
```bash
# 检查PM2状态
pm2 status

# 查看详细错误日志
pm2 logs --err

# 手动启动服务进行调试
cd /opt/chatadvisor/ChatAdvisorServer
npm start
```

#### 3. 构建失败

**症状**：前端或后端构建过程中出错

**解决方案**：
```bash
# 清理缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install

# 检查Node.js版本
node --version  # 需要20+
```

#### 4. 测试失败

**症状**：Playwright测试无法通过

**解决方案**：
```bash
# 检查服务是否正常运行
curl http://**************:34001
curl http://**************:53011/health

# 手动运行测试进行调试
cd ci/tests
npx playwright test --headed --debug
```

### 调试技巧

#### 1. 启用详细日志

在GitLab CI/CD变量中设置：
```
DEBUG=1
VERBOSE=1
```

#### 2. 保留构建产物

修改`.gitlab-ci.yml`中的artifacts配置：
```yaml
artifacts:
  when: always
  expire_in: 1 week
  paths:
    - admin-frontend/dist/
    - ChatAdvisorServer/dist/
    - ci/tests/playwright-report/
```

#### 3. 手动执行脚本

在目标服务器上手动执行部署脚本：
```bash
cd /opt/chatadvisor
./ci/scripts/deploy.sh
```

## 性能优化

### 构建优化

1. **缓存策略**：
   - 使用npm缓存减少依赖安装时间
   - 缓存node_modules目录

2. **并行构建**：
   - 前端和后端并行构建
   - 使用多个GitLab Runner

3. **增量构建**：
   - 只构建变更的组件
   - 使用构建缓存

### 部署优化

1. **零停机部署**：
   - 使用PM2的graceful reload
   - 健康检查确保服务正常

2. **回滚机制**：
   - 保留前一版本的备份
   - 快速回滚功能

## 安全考虑

### 1. SSH密钥管理

- 使用专用的部署密钥
- 定期轮换SSH密钥
- 限制密钥权限

### 2. 网络安全

- 限制GitLab Runner的网络访问
- 使用VPN或专用网络
- 配置防火墙规则

### 3. 环境变量

- 敏感信息使用GitLab CI/CD变量
- 启用变量保护和掩码
- 定期审查变量配置

## 扩展功能

### 1. 多环境部署

支持开发、测试、生产多环境：

```yaml
# 开发环境
deploy_dev:
  stage: deploy
  environment:
    name: development
    url: http://dev.chatadvisor.com

# 生产环境
deploy_prod:
  stage: deploy
  environment:
    name: production
    url: http://chatadvisor.com
  when: manual  # 手动触发
```

### 2. 容器化部署

使用Docker进行容器化部署：

```dockerfile
# Dockerfile
FROM node:20-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

### 3. 监控集成

集成监控和告警系统：

```yaml
# 部署后监控
monitor_deployment:
  stage: monitor
  script:
    - curl -X POST "https://api.uptimerobot.com/v2/newMonitor"
    - ./scripts/setup-alerts.sh
```

## 联系支持

如果遇到问题，请：

1. 查看GitLab流水线日志
2. 检查服务器日志
3. 参考本文档的故障排除部分
4. 联系技术支持团队

---

**文档版本**: v1.0  
**最后更新**: 2025-01-30  
**维护者**: ChatAdvisor DevOps Team
