# ChatAdvisor 子项目独立部署指南

## 概述

本文档介绍如何为ChatAdvisor项目的前端（admin-frontend）和后端（ChatAdvisorServer）配置独立的GitLab CI/CD流水线，实现各自独立的部署和管理。

## 架构设计

### 独立部署架构

```
admin-frontend/
├── .gitlab-ci.yml          # 前端独立CI/CD配置
├── pm2.config.js           # 前端PM2配置
├── scripts/deploy.sh       # 前端部署脚本
└── package.json

ChatAdvisorServer/
├── .gitlab-ci.yml          # 后端独立CI/CD配置
├── pm2.config.js           # 后端PM2配置（更新）
├── scripts/deploy-backend.sh # 后端部署脚本
└── package.json

scripts/
└── pm2-deploy.sh           # PM2便利部署脚本
```

### 服务端口分配

| 服务 | 生产环境 | 开发环境 | 说明 |
|------|----------|----------|------|
| 前端 | 34001 | 54001 | admin-frontend |
| 后端 | 53011 | 33001 | ChatAdvisorServer |

## 前端独立部署

### 1. GitLab CI/CD配置

前端流水线配置文件：`admin-frontend/.gitlab-ci.yml`

**触发条件**：提交信息包含 `release:`

**流水线阶段**：
1. **validate** - 验证前端环境
2. **build** - 构建前端项目
3. **deploy** - 部署到目标服务器
4. **test** - 健康检查和PM2状态检查
5. **notify** - 部署结果通知

### 2. PM2配置

前端PM2配置：`admin-frontend/pm2.config.js`

```javascript
module.exports = {
    apps: [
        {
            name: 'admin-frontend-release',
            script: 'npx',
            args: 'vite preview --port 34001 --host',
            env: { NODE_ENV: 'production', PORT: 34001 }
        },
        {
            name: 'admin-frontend-debug',
            script: 'npx',
            args: 'vite --port 54001 --host',
            env: { NODE_ENV: 'development', PORT: 54001 }
        }
    ]
};
```

### 3. 部署脚本

前端部署脚本：`admin-frontend/scripts/deploy.sh`

**使用方法**：
```bash
# 部署到生产环境
./scripts/deploy.sh

# 部署到开发环境
./scripts/deploy.sh --env development

# 仅构建
./scripts/deploy.sh --build-only

# 仅部署
./scripts/deploy.sh --deploy-only
```

### 4. 前端独立部署流程

```bash
# 1. 进入前端目录
cd admin-frontend

# 2. 创建包含release:的提交
git add .
git commit -m "release: 更新前端界面"
git push origin main

# 3. 查看GitLab流水线状态
# 访问: https://gitlab.zweiteng.tk/server/admin-frontend/-/pipelines

# 4. 手动部署（可选）
./scripts/deploy.sh --env production
```

## 后端独立部署

### 1. GitLab CI/CD配置

后端流水线配置文件：`ChatAdvisorServer/.gitlab-ci.yml`

**触发条件**：提交信息包含 `release:`

**流水线阶段**：
1. **validate** - 验证后端环境
2. **build** - 构建和测试后端项目
3. **deploy** - 部署到目标服务器
4. **test** - 健康检查、数据库检查、PM2状态检查
5. **notify** - 部署结果通知

### 2. PM2配置

后端PM2配置：`ChatAdvisorServer/pm2.config.js`（已更新）

```javascript
module.exports = {
    apps: [
        {
            name: 'chat-advisor-release',
            script: 'dist/src/index.js',
            env: { NODE_ENV: 'production', PORT: 53011 }
        },
        {
            name: 'chat-advisor-debug',
            script: 'dist/src/index.js',
            env: { NODE_ENV: 'debug', PORT: 33001 }
        }
    ]
};
```

### 3. 部署脚本

后端部署脚本：`ChatAdvisorServer/scripts/deploy-backend.sh`

**使用方法**：
```bash
# 部署到生产环境
./scripts/deploy-backend.sh

# 部署到开发环境
./scripts/deploy-backend.sh --env development

# 运行数据库迁移
./scripts/deploy-backend.sh --migrate

# 仅构建
./scripts/deploy-backend.sh --build-only
```

### 4. 后端独立部署流程

```bash
# 1. 进入后端目录
cd ChatAdvisorServer

# 2. 创建包含release:的提交
git add .
git commit -m "release: 更新API接口"
git push origin main

# 3. 查看GitLab流水线状态
# 访问: https://gitlab.zweiteng.tk/server/chatadvisor-server/-/pipelines

# 4. 手动部署（可选）
./scripts/deploy-backend.sh --env production
```

## PM2便利部署

### 统一管理脚本

便利部署脚本：`scripts/pm2-deploy.sh`

### 使用方法

```bash
# 部署前端到生产环境
./scripts/pm2-deploy.sh frontend deploy production

# 部署后端到开发环境
./scripts/pm2-deploy.sh backend deploy development

# 部署所有服务
./scripts/pm2-deploy.sh all deploy production

# 查看服务状态
./scripts/pm2-deploy.sh all status

# 查看前端日志
./scripts/pm2-deploy.sh frontend logs production

# 重启后端服务
./scripts/pm2-deploy.sh backend restart production

# 健康检查
./scripts/pm2-deploy.sh all health production
```

### 快捷命令

```bash
# 显示帮助
./scripts/pm2-deploy.sh --help

# 显示版本
./scripts/pm2-deploy.sh --version

# 列出所有服务
./scripts/pm2-deploy.sh --list
```

## GitLab CI/CD变量配置

### 必需变量

在GitLab项目的CI/CD设置中配置以下变量：

| 变量名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| `SSH_PRIVATE_KEY` | File | SSH私钥 | (私钥内容) |
| `DEPLOY_HOST` | Variable | 部署主机 | ************** |
| `DEPLOY_USER` | Variable | 部署用户 | root |
| `DEPLOY_PATH` | Variable | 部署路径 | /opt/chatadvisor |

### 前端项目变量

在admin-frontend项目中额外配置：

| 变量名 | 值 |
|--------|-----|
| `FRONTEND_PORT` | 34001 |
| `SERVICE_NAME` | admin-frontend-release |

### 后端项目变量

在ChatAdvisorServer项目中额外配置：

| 变量名 | 值 |
|--------|-----|
| `BACKEND_PORT` | 53011 |
| `SERVICE_NAME` | chat-advisor-release |

## 监控和日志

### PM2状态监控

```bash
# 查看所有服务状态
pm2 status

# 查看特定服务详情
pm2 show admin-frontend-release
pm2 show chat-advisor-release

# 实时监控
pm2 monit
```

### 日志管理

```bash
# 查看前端日志
pm2 logs admin-frontend-release

# 查看后端日志
pm2 logs chat-advisor-release

# 查看所有日志
pm2 logs

# 清空日志
pm2 flush
```

### 日志文件位置

**前端日志**：
- `admin-frontend/logs/admin-release-combined.log`
- `admin-frontend/logs/admin-release-error.log`
- `admin-frontend/logs/admin-release-out.log`

**后端日志**：
- `ChatAdvisorServer/logs/release-combined.log`
- `ChatAdvisorServer/logs/release-error.log`
- `ChatAdvisorServer/logs/release-out.log`

## 故障排除

### 常见问题

#### 1. 前端部署失败

**症状**：前端构建或部署过程中出错

**解决方案**：
```bash
# 检查Node.js版本
node --version  # 需要20+

# 清理缓存
cd admin-frontend
npm cache clean --force
rm -rf node_modules package-lock.json
npm install

# 手动构建测试
npm run build:prod

# 检查PM2状态
pm2 status
pm2 logs admin-frontend-release
```

#### 2. 后端部署失败

**症状**：后端编译或启动失败

**解决方案**：
```bash
# 检查TypeScript编译
cd ChatAdvisorServer
npm run build

# 检查环境配置
cat .env | grep -E "OPENAI_API_KEY|MONGODB_URI|JWT_SECRET"

# 检查数据库连接
npm run verify-config

# 检查PM2状态
pm2 status
pm2 logs chat-advisor-release
```

#### 3. 服务无法访问

**症状**：服务启动但无法通过浏览器访问

**解决方案**：
```bash
# 检查端口占用
netstat -tlnp | grep :34001  # 前端
netstat -tlnp | grep :53011  # 后端

# 检查防火墙
ufw status

# 检查服务绑定
curl http://localhost:34001  # 前端
curl http://localhost:53011/health  # 后端

# 重启服务
pm2 restart admin-frontend-release
pm2 restart chat-advisor-release
```

### 调试技巧

#### 1. 启用详细日志

在GitLab CI/CD变量中设置：
```
DEBUG=1
VERBOSE=1
```

#### 2. 手动执行部署脚本

```bash
# 前端手动部署
cd admin-frontend
./scripts/deploy.sh --env production

# 后端手动部署
cd ChatAdvisorServer
./scripts/deploy-backend.sh --env production
```

#### 3. 使用PM2便利脚本调试

```bash
# 检查所有服务健康状态
./scripts/pm2-deploy.sh all health production

# 查看详细状态
./scripts/pm2-deploy.sh all status

# 重启所有服务
./scripts/pm2-deploy.sh all restart production
```

## 最佳实践

### 1. 部署策略

- **前端**：适合频繁部署，UI更新不影响后端
- **后端**：谨慎部署，确保API兼容性
- **数据库迁移**：后端部署时使用`--migrate`参数

### 2. 版本管理

```bash
# 使用语义化版本号
git commit -m "release: v1.2.0 - 新增用户管理功能"
git commit -m "release: v1.2.1 - 修复登录问题"
```

### 3. 环境隔离

- 生产环境使用`production`配置
- 开发环境使用`development`配置
- 避免在生产环境运行开发版本

### 4. 监控告警

```bash
# 设置PM2自动重启
pm2 startup
pm2 save

# 定期健康检查
*/5 * * * * /opt/chatadvisor/scripts/pm2-deploy.sh all health production
```

## 扩展功能

### 1. 蓝绿部署

```bash
# 部署到备用环境
./scripts/pm2-deploy.sh frontend deploy development

# 验证无误后切换到生产
./scripts/pm2-deploy.sh frontend stop production
./scripts/pm2-deploy.sh frontend start development
```

### 2. 回滚机制

```bash
# 查看备份
ls -la admin-frontend/dist.backup.*
ls -la ChatAdvisorServer/dist.backup.*

# 手动回滚
cp -r admin-frontend/dist.backup.20250130_143000 admin-frontend/dist
pm2 restart admin-frontend-release
```

### 3. 自动化测试集成

在`.gitlab-ci.yml`中添加测试阶段：

```yaml
test_frontend:
  stage: test
  script:
    - npm test
    - npm run e2e
```

---

**文档版本**: v1.0  
**最后更新**: 2025-01-30  
**维护者**: ChatAdvisor DevOps Team
