# 版本控制系统部署和维护指南

## 概述

本文档提供版本控制系统的完整部署、配置和维护指南，确保系统在生产环境中稳定运行。

## 部署前准备

### 系统要求

**后端服务器：**
- Node.js 16.x 或更高版本
- MongoDB 4.4 或更高版本
- 内存：至少 2GB RAM
- 存储：至少 10GB 可用空间

**管理后台：**
- 现代浏览器支持（Chrome 90+, Firefox 88+, Safari 14+）
- HTTPS 支持（生产环境必需）

**移动客户端：**
- iOS 12.0 或更高版本
- Android API Level 21 或更高版本

### 环境变量配置

创建 `.env` 文件：

```bash
# 数据库配置
MONGODB_URI=mongodb://localhost:27017/chatadvisor
MONGODB_DB_NAME=chatadvisor

# 服务器配置
PORT=33001
NODE_ENV=production

# 版本控制配置
VERSION_CHECK_ENABLED=true
DEFAULT_LATEST_VERSION=1.0.0
DEFAULT_MINIMUM_VERSION=1.0.0

# 应用商店链接（可选，可在管理后台配置）
IOS_APP_STORE_URL=https://apps.apple.com/app/your-app-id
ANDROID_PLAY_STORE_URL=https://play.google.com/store/apps/details?id=your.package.name

# 安全配置
JWT_SECRET=your-jwt-secret-key
ADMIN_SECRET=your-admin-secret-key
```

## 部署步骤

### 1. 后端部署

#### 安装依赖
```bash
cd ChatAdvisorServer
npm install --production
```

#### 运行数据库迁移
```bash
# 执行版本控制字段迁移
npm run migrate:version-control

# 验证迁移结果
npm run migrate:version-control -- --validate
```

#### 启动服务
```bash
# 生产环境启动
npm run start

# 或使用 PM2 管理进程
pm2 start ecosystem.config.js
```

#### PM2 配置文件 (ecosystem.config.js)
```javascript
module.exports = {
  apps: [{
    name: 'chatadvisor-server',
    script: 'dist/index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 33001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
```

### 2. 管理后台部署

#### 构建生产版本
```bash
cd admin-frontend
npm install
npm run build
```

#### Nginx 配置
```nginx
server {
    listen 80;
    server_name your-admin-domain.com;
    
    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-admin-domain.com;
    
    # SSL 证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # 静态文件服务
    location / {
        root /path/to/admin-frontend/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存策略
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://localhost:33001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 3. 移动客户端部署

#### iOS 应用
1. 更新 `Info.plist` 中的版本号
2. 确保网络请求包含正确的版本头信息
3. 测试版本检测和更新提示功能
4. 提交到 App Store

#### Android 应用
1. 更新 `build.gradle` 中的版本号
2. 确保网络请求包含正确的版本头信息
3. 测试版本检测和更新提示功能
4. 发布到 Google Play Store

## 初始配置

### 1. 创建管理员账户
```bash
cd ChatAdvisorServer
npm run admin:create
```

### 2. 配置版本控制
1. 登录管理后台
2. 导航到：系统管理 → 版本控制
3. 设置初始配置：

```json
{
  "latestVersion": "1.0.0",
  "minimumVersion": "1.0.0",
  "forceUpdate": false,
  "updateMessage": {
    "zh_CN": "发现新版本，建议立即更新以获得更好的体验。",
    "en": "New version available, please update for better experience."
  },
  "appStoreUrls": {
    "ios": "https://apps.apple.com/app/your-app-id",
    "android": "https://play.google.com/store/apps/details?id=your.package.name"
  },
  "updateType": "optional",
  "versionCheckEnabled": true
}
```

## 监控和日志

### 1. 应用监控

#### 健康检查端点
```bash
# 检查服务器状态
curl http://localhost:33001/api/health

# 检查数据库连接
curl http://localhost:33001/api/admin/system/database
```

#### 版本检测监控
```bash
# 测试版本检测API
curl -H "App-Version: 1.0.0" \
     -H "Platform: ios" \
     -H "Local: zh_CN" \
     http://localhost:33001/api/checkVersion
```

### 2. 日志管理

#### 日志配置
```javascript
// logger.js
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'version-control' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

#### 关键日志事件
- 版本检测请求
- 配置更新操作
- 错误和异常
- 性能指标

### 3. 性能监控

#### 关键指标
- API 响应时间
- 数据库查询性能
- 内存使用情况
- 并发连接数

#### 监控工具推荐
- **APM**: New Relic, DataDog
- **日志**: ELK Stack, Splunk
- **基础设施**: Prometheus + Grafana

## 维护操作

### 1. 版本发布流程

#### 发布新版本
1. **更新版本配置**：
   ```bash
   # 通过管理后台或API更新
   curl -X PUT http://localhost:33001/api/admin/system/version-config \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -d '{
          "latestVersion": "1.1.0",
          "updateMessage": {
            "zh_CN": "新版本包含性能优化和错误修复",
            "en": "New version includes performance improvements and bug fixes"
          }
        }'
   ```

2. **验证配置**：
   ```bash
   # 测试版本检测
   curl -H "App-Version: 1.0.0" \
        -H "Platform: ios" \
        http://localhost:33001/api/checkVersion
   ```

3. **监控更新率**：
   - 观察用户更新行为
   - 监控API调用量
   - 检查错误日志

#### 强制更新流程
1. 评估更新的紧急程度
2. 设置合理的最低版本号
3. 配置强制更新消息
4. 逐步推送（可选）
5. 监控用户反馈

### 2. 数据库维护

#### 定期备份
```bash
# 创建备份
mongodump --uri="mongodb://localhost:27017/chatadvisor" --out=/backup/$(date +%Y%m%d)

# 恢复备份
mongorestore --uri="mongodb://localhost:27017/chatadvisor" /backup/20231201
```

#### 索引优化
```javascript
// 为版本检测相关字段创建索引
db.configs.createIndex({ "latestVersion": 1 });
db.configs.createIndex({ "versionCheckEnabled": 1 });
```

### 3. 安全维护

#### 定期安全检查
- 更新依赖包
- 检查安全漏洞
- 审查访问日志
- 更新SSL证书

#### 依赖更新
```bash
# 检查过时的依赖
npm outdated

# 更新依赖
npm update

# 安全审计
npm audit
npm audit fix
```

## 故障排除

### 常见问题

#### 1. 版本检测API返回错误
**症状**：客户端无法获取版本信息

**排查步骤**：
```bash
# 检查服务器状态
curl http://localhost:33001/api/health

# 检查数据库连接
mongo --eval "db.adminCommand('ismaster')"

# 查看错误日志
tail -f logs/error.log
```

#### 2. 管理后台无法访问
**症状**：管理后台页面无法加载

**排查步骤**：
```bash
# 检查Nginx状态
sudo systemctl status nginx

# 检查Nginx配置
sudo nginx -t

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log
```

#### 3. 数据库连接失败
**症状**：服务器无法连接到MongoDB

**排查步骤**：
```bash
# 检查MongoDB状态
sudo systemctl status mongod

# 检查连接
mongo --eval "db.runCommand({connectionStatus: 1})"

# 查看MongoDB日志
sudo tail -f /var/log/mongodb/mongod.log
```

### 应急处理

#### 禁用版本检测
```javascript
// 紧急禁用版本检测
db.configs.updateOne(
  {},
  { $set: { versionCheckEnabled: false } }
);
```

#### 回滚版本配置
```javascript
// 回滚到之前的版本配置
db.configs.updateOne(
  {},
  { 
    $set: { 
      latestVersion: "1.0.0",
      forceUpdate: false,
      updateType: "optional"
    } 
  }
);
```

## 性能优化

### 1. 缓存策略
- API响应缓存
- 静态资源缓存
- 数据库查询缓存

### 2. 负载均衡
- 使用Nginx进行负载均衡
- 配置多个后端实例
- 实现健康检查

### 3. 数据库优化
- 合理的索引设计
- 查询优化
- 连接池配置

## 扩展和升级

### 1. 水平扩展
- 增加服务器实例
- 配置负载均衡器
- 实现会话共享

### 2. 功能扩展
- A/B测试支持
- 渐进式发布
- 自定义更新渠道

### 3. 版本升级
- 制定升级计划
- 执行数据迁移
- 验证功能完整性
