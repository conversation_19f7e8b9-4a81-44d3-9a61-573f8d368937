# 版本控制系统类型定义

## 概述

本文档包含版本控制系统的完整TypeScript类型定义，适用于前端、后端和移动端开发。

## 核心类型定义

### 版本控制配置

```typescript
/**
 * 版本控制配置接口
 */
interface VersionControlConfig {
  /** 最新版本号（格式：x.y.z） */
  latestVersion: string;
  /** 最低支持版本号（格式：x.y.z） */
  minimumVersion: string;
  /** 是否强制更新 */
  forceUpdate: boolean;
  /** 更新提示消息（多语言支持） */
  updateMessage: Record<string, string>;
  /** 应用商店下载链接 */
  appStoreUrls: {
    ios: string;
    android: string;
  };
  /** 更新类型 */
  updateType: 'force' | 'optional';
  /** 是否启用版本检测 */
  versionCheckEnabled: boolean;
}

/**
 * 版本控制配置更新数据
 */
interface VersionControlUpdateData {
  latestVersion?: string;
  minimumVersion?: string;
  forceUpdate?: boolean;
  updateMessage?: Record<string, string>;
  appStoreUrls?: {
    ios?: string;
    android?: string;
  };
  updateType?: 'force' | 'optional';
  versionCheckEnabled?: boolean;
}
```

### 版本检测响应

```typescript
/**
 * 版本检测响应数据
 */
interface VersionCheckResponse {
  /** 是否需要更新 */
  needUpdate: boolean;
  /** 更新类型 */
  updateType: 'force' | 'optional' | 'none';
  /** 是否可以继续使用应用 */
  canUseApp?: boolean;
  /** 当前版本号 */
  currentVersion: string;
  /** 最新版本号 */
  latestVersion: string;
  /** 最低支持版本号 */
  minimumVersion: string;
  /** 更新提示消息 */
  updateMessage: string;
  /** 下载链接 */
  downloadUrl: string;
  /** 平台类型 */
  platform: string;
  /** 版本信息详情 */
  versionInfo: {
    /** 是否为最新版本 */
    isLatest: boolean;
    /** 是否低于最低版本 */
    isBelowMinimum: boolean;
    /** 是否有新版本 */
    hasNewVersion: boolean;
  };
}

/**
 * 版本信息响应
 */
interface VersionInfoResponse {
  latestVersion: string;
  minimumVersion: string;
  versionCheckEnabled: boolean;
  updateType: 'force' | 'optional';
  appStoreUrls: {
    ios: string;
    android: string;
  };
}
```

### 系统配置扩展

```typescript
/**
 * 系统配置接口（扩展版本控制）
 */
interface SystemConfig {
  _id?: string;
  privacyPolicy: string;
  termsOfService: string;
  appVersion: string;
  supportEmail: string;
  featureFlags: Record<string, boolean>;
  mainSolgan: Record<string, string[]>;
  registerSolgan: Record<string, string[]>;
  emailLoginSolgan: Record<string, string[]>;
  rechargeMessages: Record<string, string[]>;
  hideMessage: Record<string, string[]>;
  rechargeDescription: Record<string, string>;
  promotLocal?: Record<string, string>;
  promotCloud?: Record<string, string>;
  compressRate: number;
  
  // 版本控制相关字段
  latestVersion: string;
  minimumVersion: string;
  forceUpdate: boolean;
  updateMessage: Record<string, string>;
  appStoreUrls: {
    ios: string;
    android: string;
  };
  updateType: 'force' | 'optional';
  versionCheckEnabled: boolean;
  
  createdAt?: string;
  updatedAt?: string;
}

/**
 * 配置响应数据（包含版本控制信息）
 */
interface ConfigResponse extends Omit<SystemConfig, 'updateMessage' | 'appStoreUrls'> {
  /** 版本控制信息 */
  versionControl: {
    needUpdate: boolean;
    updateType: 'force' | 'optional' | 'none';
    latestVersion: string;
    minimumVersion: string;
    updateMessage: string;
    downloadUrl: string;
    versionCheckEnabled: boolean;
  };
}
```

## 枚举类型

### 版本检测状态

```typescript
/**
 * 版本检测状态枚举
 */
enum VersionCheckStatus {
  /** 空闲状态 */
  IDLE = 'idle',
  /** 检测中 */
  CHECKING = 'checking',
  /** 已是最新版本 */
  UP_TO_DATE = 'up_to_date',
  /** 有可用更新 */
  UPDATE_AVAILABLE = 'update_available',
  /** 需要强制更新 */
  UPDATE_REQUIRED = 'update_required',
  /** 检测错误 */
  ERROR = 'error'
}

/**
 * 更新类型枚举
 */
enum UpdateType {
  /** 无需更新 */
  NONE = 'none',
  /** 可选更新 */
  OPTIONAL = 'optional',
  /** 强制更新 */
  FORCE = 'force'
}

/**
 * 平台类型枚举
 */
enum Platform {
  /** iOS平台 */
  IOS = 'ios',
  /** Android平台 */
  ANDROID = 'android',
  /** Web平台 */
  WEB = 'web'
}
```

### 版本检测事件

```typescript
/**
 * 版本检测事件类型
 */
enum VersionCheckEvents {
  /** 检测开始 */
  CHECK_STARTED = 'version_check_started',
  /** 检测完成 */
  CHECK_COMPLETED = 'version_check_completed',
  /** 检测失败 */
  CHECK_FAILED = 'version_check_failed',
  /** 发现可用更新 */
  UPDATE_AVAILABLE = 'update_available',
  /** 需要强制更新 */
  UPDATE_REQUIRED = 'update_required',
  /** 用户跳过更新 */
  UPDATE_SKIPPED = 'update_skipped',
  /** 用户选择稍后更新 */
  UPDATE_LATER = 'update_later',
  /** 开始更新 */
  UPDATE_STARTED = 'update_started'
}
```

## Hook 类型定义

### useVersionCheck Hook

```typescript
/**
 * 版本检测Hook选项
 */
interface UseVersionCheckOptions {
  /** 是否自动检查版本 */
  autoCheck?: boolean;
  /** 检查间隔（毫秒），默认24小时 */
  checkInterval?: number;
  /** 是否在应用启动时检查 */
  checkOnMount?: boolean;
  /** 平台类型 */
  platform?: 'ios' | 'android';
}

/**
 * 版本检测Hook返回值
 */
interface UseVersionCheckReturn {
  /** 版本检查结果 */
  versionInfo: VersionCheckResponse | null;
  /** 是否正在检查版本 */
  isChecking: boolean;
  /** 检查错误信息 */
  error: string | null;
  /** 手动检查版本 */
  checkVersion: () => Promise<void>;
  /** 清除版本检查结果 */
  clearVersionInfo: () => void;
  /** 是否需要更新 */
  needsUpdate: boolean;
  /** 是否为强制更新 */
  isForceUpdate: boolean;
}
```

## 组件 Props 类型

### UpdatePrompt 组件

```typescript
/**
 * 更新提示组件Props
 */
interface UpdatePromptProps {
  /** 版本检查结果 */
  versionInfo: VersionCheckResponse;
  /** 是否显示提示 */
  isOpen: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 更新回调 */
  onUpdate?: () => void;
  /** 稍后提醒回调 */
  onLater?: () => void;
  /** 跳过版本回调 */
  onSkip?: () => void;
}
```

## 工具类型

### 版本比较

```typescript
/**
 * 版本比较结果
 */
type VersionComparisonResult = -1 | 0 | 1;

/**
 * 版本比较函数类型
 */
type VersionComparator = (version1: string, version2: string) => VersionComparisonResult;

/**
 * 版本验证函数类型
 */
type VersionValidator = (version: string) => boolean;
```

### 错误类型

```typescript
/**
 * 版本检测错误类
 */
class VersionCheckError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'VersionCheckError';
  }
}

/**
 * 版本检测错误代码
 */
enum VersionCheckErrorCode {
  /** 网络错误 */
  NETWORK_ERROR = 'NETWORK_ERROR',
  /** 配置错误 */
  CONFIG_ERROR = 'CONFIG_ERROR',
  /** 版本格式错误 */
  INVALID_VERSION = 'INVALID_VERSION',
  /** API错误 */
  API_ERROR = 'API_ERROR',
  /** 未知错误 */
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}
```

## API 请求/响应类型

### 网络请求

```typescript
/**
 * API响应基础结构
 */
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
  success?: boolean;
}

/**
 * 版本检测请求头
 */
interface VersionCheckHeaders {
  'App-Version': string;
  'Platform': 'ios' | 'android';
  'Local': string;
}

/**
 * 管理后台API响应
 */
interface AdminApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
}
```

## 配置类型

### 版本检测配置

```typescript
/**
 * 版本检测配置接口
 */
interface VersionCheckConfig {
  /** 是否启用版本检测 */
  enabled: boolean;
  /** 检查间隔（毫秒） */
  checkInterval: number;
  /** 是否在应用启动时检查 */
  checkOnMount: boolean;
  /** 平台类型 */
  platform: 'ios' | 'android';
  /** 是否启用自动检查 */
  autoCheck: boolean;
  /** 强制更新倒计时时间（秒） */
  forceUpdateCountdown: number;
  /** 版本检测API端点 */
  apiEndpoint: string;
}

/**
 * 本地存储键名
 */
interface StorageKeys {
  readonly VERSION_CHECK_DATA: string;
  readonly LAST_CHECK_TIME: string;
  readonly SKIPPED_VERSION: string;
  readonly LAST_SHOWN_VERSION: string;
  readonly VERSION_CONFIG: string;
}
```

## 使用示例

### TypeScript 项目中使用

```typescript
// 导入类型
import type { 
  VersionCheckResponse, 
  VersionControlConfig,
  UseVersionCheckOptions 
} from './types/version-control';

// 使用Hook
const versionCheckOptions: UseVersionCheckOptions = {
  autoCheck: true,
  platform: 'ios',
  checkInterval: 24 * 60 * 60 * 1000
};

const { versionInfo, needsUpdate } = useVersionCheck(versionCheckOptions);

// 处理版本信息
if (versionInfo && needsUpdate) {
  console.log(`需要从 ${versionInfo.currentVersion} 更新到 ${versionInfo.latestVersion}`);
}
```

### React 组件中使用

```typescript
import React from 'react';
import type { UpdatePromptProps } from './types/version-control';

const UpdatePrompt: React.FC<UpdatePromptProps> = ({
  versionInfo,
  isOpen,
  onClose,
  onUpdate
}) => {
  // 组件实现
  return (
    // JSX
  );
};
```

## Swift/iOS 类型定义

### 版本控制模型

```swift
// 版本控制信息
struct VersionControl: Codable {
    let needUpdate: Bool
    let updateType: UpdateType
    let latestVersion: String
    let minimumVersion: String
    let updateMessage: String
    let downloadUrl: String
    let versionCheckEnabled: Bool

    enum UpdateType: String, Codable, CaseIterable {
        case none = "none"
        case optional = "optional"
        case force = "force"

        var isForceUpdate: Bool {
            return self == .force
        }

        var needsUpdate: Bool {
            return self != .none
        }
    }
}

// 版本检测响应
struct VersionCheckResponse: Codable {
    let needUpdate: Bool
    let updateType: String
    let currentVersion: String
    let latestVersion: String
    let minimumVersion: String
    let updateMessage: String
    let downloadUrl: String
    let platform: String
}
```

## 注意事项

1. **版本号格式**：所有版本号必须遵循 `x.y.z` 格式
2. **类型安全**：使用 TypeScript 严格模式确保类型安全
3. **向后兼容**：新增字段应为可选，避免破坏现有代码
4. **错误处理**：使用专门的错误类型处理版本检测错误
5. **事件类型**：使用枚举定义事件类型，避免字符串拼写错误
6. **多平台一致性**：确保不同平台使用相同的数据结构
7. **本地化支持**：字符串类型字段应支持多语言
