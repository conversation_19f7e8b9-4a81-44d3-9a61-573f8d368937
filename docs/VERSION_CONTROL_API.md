# 版本控制系统 API 文档

## 概述

版本控制系统提供了完整的应用版本管理功能，包括版本检测、强制更新、可选更新等特性。支持iOS和Android平台，提供多语言更新提示。

## API 端点

### 1. 版本检测 API

#### 检查版本更新
```
GET /api/checkVersion
GET /checkVersion (兼容性路由)
```

**请求头参数：**
- `App-Version` (string): 客户端当前版本号，格式：x.y.z
- `Platform` (string): 平台类型，可选值：ios, android
- `Local` (string): 语言代码，如：zh_CN, en

**响应示例：**
```json
{
  "code": 200,
  "message": "版本检测完成",
  "data": {
    "needUpdate": true,
    "updateType": "optional",
    "canUseApp": true,
    "currentVersion": "1.0.0",
    "latestVersion": "1.2.0",
    "minimumVersion": "1.0.0",
    "updateMessage": "发现新版本，建议立即更新以获得更好的体验",
    "downloadUrl": "https://apps.apple.com/app/your-app-id",
    "platform": "ios",
    "versionInfo": {
      "isLatest": false,
      "isBelowMinimum": false,
      "hasNewVersion": true
    }
  }
}
```

#### 获取版本信息
```
GET /api/versionInfo
GET /versionInfo (兼容性路由)
```

**响应示例：**
```json
{
  "code": 200,
  "message": "获取版本信息成功",
  "data": {
    "latestVersion": "1.2.0",
    "minimumVersion": "1.0.0",
    "versionCheckEnabled": true,
    "updateType": "optional",
    "appStoreUrls": {
      "ios": "https://apps.apple.com/app/your-app-id",
      "android": "https://play.google.com/store/apps/details?id=your.package.name"
    }
  }
}
```

### 2. 配置获取 API

#### 获取应用配置（包含版本控制信息）
```
GET /api/getConfig
GET /getConfig (兼容性路由)
```

**请求头参数：**
- `App-Version` (string): 客户端当前版本号
- `Platform` (string): 平台类型
- `Local` (string): 语言代码

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "appVersion": "1.0.0",
    "privacyPolicy": "https://example.com/privacy",
    "termsOfService": "https://example.com/terms",
    "supportEmail": "<EMAIL>",
    "featureFlags": {},
    "compressRate": 0.8,
    "versionControl": {
      "needUpdate": true,
      "updateType": "optional",
      "latestVersion": "1.2.0",
      "minimumVersion": "1.0.0",
      "updateMessage": "发现新版本，建议立即更新",
      "downloadUrl": "https://apps.apple.com/app/your-app-id",
      "versionCheckEnabled": true
    }
  }
}
```

### 3. 管理后台 API

#### 获取版本控制配置
```
GET /api/admin/system/version-config
```

**认证：** 需要管理员认证

**响应示例：**
```json
{
  "success": true,
  "data": {
    "latestVersion": "1.2.0",
    "minimumVersion": "1.0.0",
    "forceUpdate": false,
    "updateMessage": {
      "zh_CN": "发现新版本，建议立即更新以获得更好的体验",
      "en": "New version available, please update for better experience"
    },
    "appStoreUrls": {
      "ios": "https://apps.apple.com/app/your-app-id",
      "android": "https://play.google.com/store/apps/details?id=your.package.name"
    },
    "updateType": "optional",
    "versionCheckEnabled": true
  }
}
```

#### 更新版本控制配置
```
PUT /api/admin/system/version-config
```

**认证：** 需要管理员认证

**请求体：**
```json
{
  "latestVersion": "1.3.0",
  "minimumVersion": "1.1.0",
  "forceUpdate": true,
  "updateMessage": {
    "zh_CN": "此版本包含重要的安全更新，必须立即更新",
    "en": "This version contains important security updates, must update immediately"
  },
  "appStoreUrls": {
    "ios": "https://apps.apple.com/app/your-app-id",
    "android": "https://play.google.com/store/apps/details?id=your.package.name"
  },
  "updateType": "force",
  "versionCheckEnabled": true
}
```

#### 测试版本检查
```
GET /api/admin/system/check-version?platform=ios
```

**认证：** 需要管理员认证

**查询参数：**
- `platform` (string): 平台类型，可选值：ios, android

## 版本号格式

所有版本号必须遵循语义化版本规范（Semantic Versioning）：

- 格式：`MAJOR.MINOR.PATCH`
- 示例：`1.2.3`
- 正则表达式：`^\d+\.\d+\.\d+$`

## 更新类型

- `none`: 无需更新
- `optional`: 可选更新（用户可以选择稍后更新或跳过）
- `force`: 强制更新（用户必须更新才能继续使用）

## 版本比较逻辑

1. **需要更新判断：**
   - 如果客户端版本低于最低支持版本：强制更新
   - 如果有新版本可用：根据配置决定是否强制更新

2. **版本比较规则：**
   - 按 MAJOR.MINOR.PATCH 顺序比较
   - 数字大的版本号更新
   - 1.2.1 > 1.2.0 > 1.1.9

## 错误代码

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权（管理后台API）
- `404`: 配置未找到
- `500`: 服务器内部错误

## 使用示例

### iOS 客户端检查版本
```swift
// 在请求头中包含版本信息
headers["App-Version"] = "1.0.0"
headers["Platform"] = "ios"
headers["Local"] = "zh_CN"

// 发送请求到 /checkVersion
```

### Android 客户端检查版本
```java
// 在请求头中包含版本信息
headers.put("App-Version", "1.0.0");
headers.put("Platform", "android");
headers.put("Local", "en");

// 发送请求到 /checkVersion
```

### Web 管理后台检查版本
```javascript
// 使用管理后台API
const response = await fetch('/api/admin/system/check-version?platform=ios', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```

## 注意事项

1. **向后兼容性：** 所有API都提供兼容性路由，确保旧版本客户端正常工作
2. **多语言支持：** 更新消息支持多语言，根据客户端语言返回对应消息
3. **平台区分：** iOS和Android使用不同的应用商店链接
4. **缓存策略：** 客户端应实现适当的缓存策略，避免频繁请求
5. **错误处理：** 网络错误时应使用本地缓存的配置信息

## 安全考虑

1. **管理后台API需要认证**
2. **版本号格式验证**
3. **输入参数校验**
4. **应用商店链接验证**
