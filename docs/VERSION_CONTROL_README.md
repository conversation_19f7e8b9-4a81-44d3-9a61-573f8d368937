# ChatAdvisor 版本控制系统

## 🚀 概述

ChatAdvisor 版本控制系统是一个完整的应用版本管理解决方案，支持多平台（iOS、Android、Web）的版本检测、更新提示和强制更新功能。

### ✨ 主要特性

- 🔄 **自动版本检测** - 智能检测客户端版本，支持自定义检测间隔
- 📱 **多平台支持** - 支持iOS、Android和Web平台
- 🌍 **多语言支持** - 更新提示支持多语言本地化
- ⚡ **灵活更新策略** - 支持强制更新和可选更新
- 🎯 **智能用户体验** - 用户友好的更新提示界面
- 📊 **管理后台** - 完整的版本控制配置管理界面
- 🔒 **安全可靠** - 完善的错误处理和安全机制

## 📋 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    ChatAdvisor 版本控制系统                  │
├─────────────────────────────────────────────────────────────┤
│  客户端层                                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ iOS 客户端  │  │Android客户端│  │ Web 管理后台│        │
│  │ - 版本检测  │  │ - 版本检测  │  │ - 版本配置  │        │
│  │ - 更新提示  │  │ - 更新提示  │  │ - 测试工具  │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
├─────────────────────────────────────────────────────────────┤
│  API 服务层                                                 │
│  ┌─────────────────────────────────────────────────────────┐│
│  │ Express.js 后端服务                                     ││
│  │ - 版本检测 API (/api/checkVersion)                     ││
│  │ - 配置获取 API (/api/getConfig)                        ││
│  │ - 管理后台 API (/api/admin/system/version-config)      ││
│  │ - 兼容性路由支持                                       ││
│  └─────────────────────────────────────────────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  数据存储层                                                 │
│  ┌─────────────────────────────────────────────────────────┐│
│  │ MongoDB 数据库                                          ││
│  │ - Config 集合（版本控制配置）                           ││
│  │ - 多语言更新消息                                       ││
│  │ - 应用商店链接配置                                     ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 🛠️ 技术栈

### 后端
- **Node.js** + **Express.js** + **TypeScript**
- **MongoDB** 数据库
- **Mongoose** ODM
- **Express Validator** 参数验证

### iOS 客户端
- **Swift** + **SwiftUI**
- **Moya** + **Alamofire** 网络请求
- **Combine** 响应式编程

### Web 管理后台
- **React** + **TypeScript**
- **Vite** 构建工具
- **Tailwind CSS** 样式框架
- **React Hook Form** 表单管理

## 📦 快速开始

### 1. 环境准备

```bash
# 安装 Node.js 16+
node --version

# 安装 MongoDB 4.4+
mongod --version

# 克隆项目
git clone <repository-url>
cd chatadvisor
```

### 2. 后端设置

```bash
cd ChatAdvisorServer

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 运行数据库迁移
npm run migrate:version-control

# 启动开发服务器
npm run dev
```

### 3. 管理后台设置

```bash
cd admin-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 4. iOS 客户端设置

```bash
cd ChatAdvisor

# 使用 Xcode 打开项目
open ChatAdvisor.xcodeproj

# 或使用 Xcode 命令行工具
xcodebuild -project ChatAdvisor.xcodeproj -scheme ChatAdvisor build
```

## 📚 文档

### 核心文档
- [📖 使用指南](./VERSION_CONTROL_GUIDE.md) - 详细的使用说明和最佳实践
- [🔌 API 文档](./VERSION_CONTROL_API.md) - 完整的API接口文档
- [🏗️ 部署指南](./VERSION_CONTROL_DEPLOYMENT.md) - 生产环境部署和维护
- [📝 类型定义](./VERSION_CONTROL_TYPES.md) - TypeScript类型定义

### 功能特性
- **版本检测机制** - 智能的版本比较和检测逻辑
- **更新策略** - 灵活的强制更新和可选更新配置
- **多语言支持** - 完整的国际化解决方案
- **用户体验** - 优雅的更新提示界面设计

## 🎯 核心功能

### 版本检测
- ✅ 语义化版本号比较（x.y.z格式）
- ✅ 最低版本支持检查
- ✅ 智能检测间隔控制
- ✅ 网络异常处理

### 更新提示
- ✅ 强制更新模式（阻止应用使用）
- ✅ 可选更新模式（用户可选择）
- ✅ 跳过版本功能
- ✅ 稍后提醒功能

### 管理配置
- ✅ 可视化配置界面
- ✅ 实时配置更新
- ✅ 版本检测测试工具
- ✅ 多语言消息配置

### 平台支持
- ✅ iOS 原生应用
- ✅ Android 应用（架构支持）
- ✅ Web 管理后台
- ✅ 跨平台API兼容

## 🔧 配置示例

### 基础配置
```json
{
  "latestVersion": "1.2.0",
  "minimumVersion": "1.0.0",
  "forceUpdate": false,
  "updateType": "optional",
  "versionCheckEnabled": true,
  "updateMessage": {
    "zh_CN": "发现新版本，建议立即更新以获得更好的体验。",
    "en": "New version available, please update for better experience."
  },
  "appStoreUrls": {
    "ios": "https://apps.apple.com/app/your-app-id",
    "android": "https://play.google.com/store/apps/details?id=your.package.name"
  }
}
```

### 强制更新配置
```json
{
  "latestVersion": "2.0.0",
  "minimumVersion": "2.0.0",
  "forceUpdate": true,
  "updateType": "force",
  "updateMessage": {
    "zh_CN": "此版本包含重要的安全更新，必须立即更新才能继续使用。",
    "en": "This version contains important security updates. You must update to continue using the app."
  }
}
```

## 🧪 测试

### 运行测试
```bash
# 后端测试
cd ChatAdvisorServer
npm test

# 前端测试
cd admin-frontend
npm test

# iOS 测试
cd ChatAdvisor
xcodebuild test -project ChatAdvisor.xcodeproj -scheme ChatAdvisorTests
```

### 手动测试
```bash
# 测试版本检测API
curl -H "App-Version: 1.0.0" \
     -H "Platform: ios" \
     -H "Local: zh_CN" \
     http://localhost:33001/api/checkVersion

# 测试管理后台API
curl -H "Authorization: Bearer <token>" \
     http://localhost:33001/api/admin/system/version-config
```

## 📈 监控和分析

### 关键指标
- 版本检测API调用量
- 用户更新成功率
- 不同版本的使用分布
- 更新提示的用户行为

### 日志记录
- 版本检测请求日志
- 配置更新操作日志
- 错误和异常日志
- 性能指标日志

## 🔒 安全考虑

- ✅ 管理后台API认证授权
- ✅ 输入参数严格验证
- ✅ 版本号格式验证
- ✅ 应用商店链接验证
- ✅ HTTPS强制使用
- ✅ 错误信息安全处理

## 🚀 部署

### 开发环境
```bash
# 启动所有服务
npm run dev:all

# 或分别启动
npm run dev:server    # 后端服务
npm run dev:admin     # 管理后台
```

### 生产环境
```bash
# 构建项目
npm run build

# 使用 PM2 部署
pm2 start ecosystem.config.js

# 或使用 Docker
docker-compose up -d
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 开发规范
- 使用 TypeScript 严格模式
- 遵循 ESLint 和 Prettier 配置
- 编写单元测试
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如果您在使用过程中遇到问题，请：

1. 查看 [文档](./docs/) 获取详细信息
2. 搜索 [Issues](../../issues) 查看是否有类似问题
3. 创建新的 [Issue](../../issues/new) 描述您的问题
4. 联系开发团队获取支持

## 🎉 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

**ChatAdvisor 版本控制系统** - 让应用版本管理变得简单高效！
