# 版本控制系统使用指南

## 概述

ChatAdvisor 版本控制系统提供了完整的应用版本管理解决方案，支持：

- 🔄 自动版本检测
- 📱 多平台支持（iOS、Android、Web）
- 🌍 多语言更新提示
- ⚡ 强制更新和可选更新
- 🎯 智能更新策略
- 📊 版本使用统计

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   iOS 客户端    │    │  Android 客户端 │    │   Web 管理后台  │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      后端 API 服务       │
                    │   - 版本检测 API         │
                    │   - 配置管理 API         │
                    │   - 管理后台 API         │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      MongoDB 数据库      │
                    │   - Config 集合          │
                    │   - 版本控制配置         │
                    └─────────────────────────┘
```

## 快速开始

### 1. 后端配置

#### 运行数据库迁移
```bash
cd ChatAdvisorServer
npm run migrate:version-control
```

#### 启动服务器
```bash
npm run dev
```

### 2. 管理后台配置

#### 访问版本控制页面
1. 登录管理后台：`http://localhost:3000/login`
2. 导航到：系统管理 → 版本控制
3. 配置版本信息：

```
最新版本号：1.2.0
最低支持版本：1.0.0
更新类型：可选更新
应用商店链接：
  - iOS: https://apps.apple.com/app/your-app-id
  - Android: https://play.google.com/store/apps/details?id=your.package.name
```

### 3. 客户端集成

#### iOS 集成
```swift
// 在应用启动时检查版本
BootManager.shared.checkForUpdates()

// 监听版本更新提示
// UpdatePromptView 会自动显示
```

#### Android 集成
```java
// 在请求头中包含版本信息
headers.put("App-Version", BuildConfig.VERSION_NAME);
headers.put("Platform", "android");

// 调用版本检测API
// 处理返回的版本信息
```

#### Web 前端集成
```javascript
// 使用版本检测Hook
const { versionInfo, needsUpdate, checkVersion } = useVersionCheck({
  autoCheck: true,
  platform: 'ios'
});

// UpdatePrompt 组件会自动处理更新提示
```

## 配置说明

### 版本控制字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `latestVersion` | string | ✅ | 最新版本号（格式：x.y.z） |
| `minimumVersion` | string | ✅ | 最低支持版本号 |
| `forceUpdate` | boolean | ✅ | 是否强制更新 |
| `updateMessage` | object | ✅ | 更新提示消息（多语言） |
| `appStoreUrls` | object | ✅ | 应用商店下载链接 |
| `updateType` | string | ✅ | 更新类型（force/optional） |
| `versionCheckEnabled` | boolean | ✅ | 是否启用版本检测 |

### 更新消息配置

```json
{
  "updateMessage": {
    "zh_CN": "发现新版本，建议立即更新以获得更好的体验。",
    "en": "New version available, please update for better experience.",
    "ja": "新しいバージョンが利用可能です。より良い体験のためにアップデートしてください。"
  }
}
```

### 应用商店链接配置

```json
{
  "appStoreUrls": {
    "ios": "https://apps.apple.com/app/id1234567890",
    "android": "https://play.google.com/store/apps/details?id=com.example.app"
  }
}
```

## 更新策略

### 1. 可选更新（Optional Update）
- 用户可以选择"立即更新"、"稍后提醒"或"跳过此版本"
- 适用于功能更新、性能优化等非关键更新
- 不会阻止用户继续使用应用

### 2. 强制更新（Force Update）
- 用户必须更新才能继续使用应用
- 适用于安全更新、重大bug修复等关键更新
- 会阻止用户使用应用直到更新完成

### 3. 最低版本检查
- 如果客户端版本低于最低支持版本，自动触发强制更新
- 确保所有用户使用受支持的版本
- 便于废弃旧版本API

## 版本检测流程

```mermaid
graph TD
    A[应用启动] --> B[检查版本检测开关]
    B -->|已启用| C[获取客户端版本]
    B -->|已禁用| Z[结束]
    C --> D[调用版本检测API]
    D --> E[比较版本号]
    E --> F{需要更新?}
    F -->|否| G[记录检测结果]
    F -->|是| H{低于最低版本?}
    H -->|是| I[强制更新]
    H -->|否| J{配置强制更新?}
    J -->|是| I
    J -->|否| K[可选更新]
    I --> L[显示强制更新提示]
    K --> M[显示可选更新提示]
    L --> N[跳转应用商店]
    M --> O{用户选择}
    O -->|立即更新| N
    O -->|稍后提醒| P[记录稍后提醒]
    O -->|跳过版本| Q[记录跳过版本]
    G --> Z
    P --> Z
    Q --> Z
    N --> Z
```

## 最佳实践

### 1. 版本号管理
- 使用语义化版本号（Semantic Versioning）
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

### 2. 更新策略
- 功能更新：使用可选更新
- 安全修复：使用强制更新
- 重大bug修复：根据严重程度选择
- API变更：提前通知并设置合理的最低版本

### 3. 用户体验
- 提供清晰的更新说明
- 避免频繁的强制更新
- 在用户空闲时检查版本
- 提供离线时的降级体验

### 4. 测试建议
- 测试不同版本号的比较逻辑
- 测试强制更新和可选更新流程
- 测试网络异常情况的处理
- 测试多语言更新消息显示

## 故障排除

### 常见问题

#### 1. 版本检测不工作
**可能原因：**
- 版本检测开关未启用
- 网络连接问题
- API端点配置错误

**解决方案：**
```javascript
// 检查配置
const config = getVersionConfig();
console.log('Version check enabled:', config.enabled);

// 手动触发检查
checkVersion();
```

#### 2. 更新提示不显示
**可能原因：**
- 用户已跳过此版本
- 版本号格式错误
- 本地缓存问题

**解决方案：**
```javascript
// 清除本地缓存
localStorage.removeItem('admin_skipped_version');
localStorage.removeItem('admin_last_shown_update_version');

// 强制检查
checkVersion();
```

#### 3. 应用商店链接无效
**可能原因：**
- 链接配置错误
- 应用未上架
- 地区限制

**解决方案：**
- 检查管理后台的链接配置
- 验证应用商店链接的有效性
- 使用占位符链接进行测试

### 调试工具

#### 启用调试日志
```javascript
// 在浏览器控制台中
localStorage.setItem('debug_version_check', 'true');
```

#### 查看版本检测历史
```javascript
// 查看本地存储的版本信息
console.log(localStorage.getItem('admin_version_check_data'));
```

## 监控和分析

### 版本使用统计
- 通过日志分析不同版本的使用情况
- 监控更新成功率
- 分析用户更新行为

### 性能监控
- 版本检测API响应时间
- 更新提示显示频率
- 用户交互统计

## 扩展功能

### 1. A/B测试支持
- 为不同用户群体配置不同的更新策略
- 测试不同更新消息的效果

### 2. 渐进式发布
- 逐步向用户推送新版本
- 监控新版本的稳定性

### 3. 自定义更新渠道
- 支持Beta版本、内测版本等不同渠道
- 为不同渠道配置不同的更新策略

## 安全注意事项

1. **API安全**：管理后台API需要适当的认证和授权
2. **输入验证**：严格验证版本号格式和其他输入参数
3. **HTTPS**：确保所有API调用使用HTTPS
4. **应用商店链接验证**：验证应用商店链接的合法性
