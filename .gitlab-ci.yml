# ChatAdvisor GitLab CI/CD 流水线配置
# 当提交信息包含 "release:" 时自动触发部署

# 全局配置
variables:
  # 部署目标配置
  DEPLOY_HOST: "**************"
  DEPLOY_USER: "root"
  DEPLOY_PATH: "/opt/chatadvisor"
  
  # 项目配置
  FRONTEND_PORT: "34001"
  BACKEND_PORT: "53011"
  
  # 构建配置
  NODE_VERSION: "20"
  NPM_CACHE_DIR: ".npm"

# 缓存配置
cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - .npm/
    - admin-frontend/node_modules/
    - ChatAdvisorServer/node_modules/

# 阶段定义
stages:
  - validate
  - build
  - deploy
  - test
  - notify

# 仅在包含 "release:" 的提交信息时触发
workflow:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /release:/
      when: always
    - when: never

# 验证阶段 - 检查环境和依赖
validate_environment:
  stage: validate
  tags:
    - eva  # 使用指定的 GitLab Runner
  script:
    - echo "🔍 验证部署环境..."
    - echo "提交信息: $CI_COMMIT_MESSAGE"
    - echo "分支: $CI_COMMIT_REF_NAME"
    - echo "提交哈希: $CI_COMMIT_SHA"
    - node --version
    - npm --version
    - echo "✅ 环境验证完成"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

# 构建阶段 - 前端构建
build_frontend:
  stage: build
  tags:
    - eva
  script:
    - echo "🏗️ 构建前端项目..."
    - cd admin-frontend
    - npm ci --cache .npm --prefer-offline
    - npm run lint
    - npm run build:prod
    - echo "📦 前端构建产物大小:"
    - du -sh dist/
    - echo "✅ 前端构建完成"
  artifacts:
    name: "frontend-$CI_COMMIT_SHORT_SHA"
    paths:
      - admin-frontend/dist/
    expire_in: 1 hour
  cache:
    key: frontend-${CI_COMMIT_REF_SLUG}
    paths:
      - admin-frontend/node_modules/
      - .npm/
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

# 构建阶段 - 后端构建
build_backend:
  stage: build
  tags:
    - eva
  script:
    - echo "🏗️ 构建后端项目..."
    - cd ChatAdvisorServer
    - npm ci --cache .npm --prefer-offline
    - npm run build
    - echo "📦 后端构建产物大小:"
    - du -sh dist/
    - echo "✅ 后端构建完成"
  artifacts:
    name: "backend-$CI_COMMIT_SHORT_SHA"
    paths:
      - ChatAdvisorServer/dist/
      - ChatAdvisorServer/package.json
      - ChatAdvisorServer/pm2.config.js
    expire_in: 1 hour
  cache:
    key: backend-${CI_COMMIT_REF_SLUG}
    paths:
      - ChatAdvisorServer/node_modules/
      - .npm/
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

# 部署阶段 - 部署到生产环境
deploy_production:
  stage: deploy
  tags:
    - eva
  dependencies:
    - build_frontend
    - build_backend
  before_script:
    - echo "🚀 准备部署到生产环境..."
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H $DEPLOY_HOST >> ~/.ssh/known_hosts
  script:
    - echo "📡 连接到目标服务器: $DEPLOY_HOST"
    - |
      ssh $DEPLOY_USER@$DEPLOY_HOST << 'EOF'
        set -e
        echo "🔄 更新代码仓库..."
        cd /opt/chatadvisor
        git fetch origin
        git reset --hard origin/main
        
        echo "📦 安装后端依赖..."
        cd ChatAdvisorServer
        npm install --production
        
        echo "📦 安装前端依赖..."
        cd ../admin-frontend
        npm install
        
        echo "🏗️ 构建前端..."
        npm run build:prod
        
        echo "🏗️ 构建后端..."
        cd ../ChatAdvisorServer
        npm run build
        
        echo "🔄 重启服务..."
        pm2 stop admin-frontend-release || true
        pm2 stop chat-advisor-release || true
        
        pm2 start pm2.config.js --only admin-frontend-release
        pm2 start pm2.config.js --only chat-advisor-release
        
        echo "⏳ 等待服务启动..."
        sleep 10
        
        echo "✅ 部署完成"
      EOF
  after_script:
    - rm -f ~/.ssh/id_rsa
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

# 测试阶段 - 健康检查
health_check:
  stage: test
  tags:
    - eva
  dependencies:
    - deploy_production
  script:
    - echo "🏥 执行健康检查..."
    - |
      # 检查前端服务
      echo "检查前端服务 (端口 $FRONTEND_PORT)..."
      if curl -f -s "http://$DEPLOY_HOST:$FRONTEND_PORT" > /dev/null; then
        echo "✅ 前端服务运行正常"
      else
        echo "❌ 前端服务检查失败"
        exit 1
      fi
      
      # 检查后端服务
      echo "检查后端服务 (端口 $BACKEND_PORT)..."
      if curl -f -s "http://$DEPLOY_HOST:$BACKEND_PORT/health" > /dev/null; then
        echo "✅ 后端服务运行正常"
      else
        echo "❌ 后端服务检查失败"
        exit 1
      fi
      
      echo "🎉 所有服务健康检查通过"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

# 测试阶段 - Playwright 自动化测试
playwright_tests:
  stage: test
  tags:
    - eva
  dependencies:
    - health_check
  before_script:
    - npm install -g playwright
    - npx playwright install chromium
  script:
    - echo "🎭 执行 Playwright 自动化测试..."
    - cd ci/tests
    - npm install
    - npx playwright test --reporter=html
    - echo "✅ 自动化测试完成"
  artifacts:
    when: always
    paths:
      - ci/tests/playwright-report/
      - ci/tests/test-results/
    expire_in: 1 week
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

# 通知阶段 - 部署成功通知
notify_success:
  stage: notify
  tags:
    - eva
  dependencies:
    - playwright_tests
  script:
    - echo "🎉 部署成功通知"
    - |
      echo "部署信息:"
      echo "- 提交: $CI_COMMIT_SHORT_SHA"
      echo "- 分支: $CI_COMMIT_REF_NAME"
      echo "- 前端地址: http://$DEPLOY_HOST:$FRONTEND_PORT"
      echo "- 后端地址: http://$DEPLOY_HOST:$BACKEND_PORT"
      echo "- 流水线: $CI_PIPELINE_URL"
  when: on_success
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

# 通知阶段 - 部署失败通知
notify_failure:
  stage: notify
  tags:
    - eva
  script:
    - echo "❌ 部署失败通知"
    - echo "流水线失败，请检查日志: $CI_PIPELINE_URL"
  when: on_failure
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/
