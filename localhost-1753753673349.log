chunk-WALXKXZM.js?v=46b8e699:21580 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
authTest.ts:81 认证测试工具已挂载到 window.authTest
authTest.ts:82 可用方法:
authTest.ts:83 - authTest.simulateTokenExpiry() - 模拟token过期
authTest.ts:84 - authTest.clearAuth() - 清除认证信息
authTest.ts:85 - authTest.setValidTestToken() - 设置有效测试token
authTest.ts:86 - authTest.checkAuthStatus() - 检查当前认证状态
authTest.ts:87 - authTest.testApiRequest() - 测试API请求
routerTest.ts:140 Router测试工具已挂载到 window.routerTest
routerTest.ts:141 可用方法:
routerTest.ts:142 - routerTest.checkRouterStatus() - 检查Router状态
routerTest.ts:143 - routerTest.testNavigation(path) - 测试导航功能
routerTest.ts:144 - routerTest.checkAuthContext() - 检查认证上下文
routerTest.ts:145 - routerTest.runFullTest() - 运行完整测试
routerTest.ts:146 - routerTest.checkConsoleErrors() - 检查控制台错误
loginTest.ts:225 登录测试工具已挂载到 window.loginTest
loginTest.ts:226 可用方法:
loginTest.ts:227 - loginTest.checkAuthState() - 检查认证状态
loginTest.ts:228 - loginTest.simulateLoginSuccess() - 模拟登录成功
loginTest.ts:229 - loginTest.checkLoginLoop() - 检查登录循环
loginTest.ts:230 - loginTest.monitorRouteChanges(duration) - 监控路由变化
loginTest.ts:231 - loginTest.testLoginFlow() - 测试完整登录流程
loginTest.ts:232 - loginTest.testAutoLogout(duration) - 测试自动跳转问题
loginTest.ts:233 - loginTest.checkAuthContext() - 检查AuthContext状态
react-router-dom.js?v=46b8e699:4409 ⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
warnOnce @ react-router-dom.js?v=46b8e699:4409
react-router-dom.js?v=46b8e699:4409 ⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.
warnOnce @ react-router-dom.js?v=46b8e699:4409
performance.ts:99 [Performance] app_start: 1 undefined
performance.ts:99 [Performance] app_start: 1 undefined
index.tsx:46 开始加载仪表板数据... Object
index.tsx:46 开始加载仪表板数据... Object
useApi.ts:40 API请求失败: Object
（匿名） @ useApi.ts:40
useApi.ts:60 API执行错误: Object
（匿名） @ useApi.ts:60
index.tsx:67 加载仪表板数据失败: Error: 请求失败
    at useApi.ts:50:17
    at async Promise.all (:3000/index 3)
    at async loadAllData (index.tsx:48:74)
loadAllData @ index.tsx:67
index.tsx:88 错误详情: Object
loadAllData @ index.tsx:88
useApi.ts:40 API请求失败: Object
（匿名） @ useApi.ts:40
useApi.ts:60 API执行错误: Object
（匿名） @ useApi.ts:60
useApi.ts:40 API请求失败: Object
（匿名） @ useApi.ts:40
useApi.ts:60 API执行错误: Object
（匿名） @ useApi.ts:60
index.tsx:67 加载仪表板数据失败: Error: 请求失败
    at useApi.ts:50:17
    at async Promise.all (:3000/index 3)
    at async loadAllData (index.tsx:48:74)
loadAllData @ index.tsx:67
index.tsx:88 错误详情: Object
loadAllData @ index.tsx:88
useApi.ts:40 API请求失败: Object
（匿名） @ useApi.ts:40
useApi.ts:60 API执行错误: Object
（匿名） @ useApi.ts:60
useApi.ts:40 API请求失败: Object
（匿名） @ useApi.ts:40
useApi.ts:60 API执行错误: Object
（匿名） @ useApi.ts:60
useApi.ts:40 API请求失败: Object
（匿名） @ useApi.ts:40
useApi.ts:60 API执行错误: Object
（匿名） @ useApi.ts:60
performance.ts:99 [Performance] navigation_tcp_connect: 0.2999999523162842ms undefined
performance.ts:99 [Performance] navigation_ssl_handshake: 8.5ms undefined
performance.ts:99 [Performance] navigation_ttfb: 2.3000000715255737ms undefined
performance.ts:99 [Performance] navigation_response_time: 1.0999999046325684ms undefined
performance.ts:99 [Performance] navigation_page_load: 0.2999999523162842ms undefined
performance.ts:99 [Performance] largest_contentful_paint: 252ms undefined
useApi.ts:40 API请求失败: Object
（匿名） @ useApi.ts:40
useApi.ts:60 API执行错误: Object
（匿名） @ useApi.ts:60
useApi.ts:40 API请求失败: Object
（匿名） @ useApi.ts:40
useApi.ts:60 API执行错误: Object
（匿名） @ useApi.ts:60
performance.ts:99 [Performance] largest_contentful_paint: 352ms undefined
performance.ts:99 [Performance] resource_total_resources: 79 undefined
performance.ts:99 [Performance] resource_total_size: 21807 undefined
performance.ts:99 [Performance] resource_avg_load_time: 24.148101269444332 undefined
performance.ts:99 [Performance] resource_slow_resources: 0 undefined
performance.ts:99 [Performance] first_input_delay: 2.3000000715255737ms undefined
