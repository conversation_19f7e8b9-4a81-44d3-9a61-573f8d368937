# AI执行清单：VPN配置冲突修复

## 🎯 AI直接执行指令

### 前置条件确认
- 用户提供服务器SSH连接信息
- 确认服务器已安装nginx、sing-box、certbot
- 确认域名已解析到服务器IP

### 📋 标准执行流程

#### 步骤1：连接服务器并检查状态
```bash
# 根据用户提供的连接信息连接服务器
ssh -i ~/.ssh/KEY_FILE USER@SERVER_IP

# 检查当前状态
sudo systemctl status nginx caddy
sudo netstat -tlnp | grep ':80\|:443'
sudo netstat -tlnp | grep sing-box
find /etc -name "*dava.cf*.json"
```

#### 步骤2：备份现有配置
```bash
# 创建备份
sudo mkdir -p /root/vpn_backup_$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/root/vpn_backup_$(date +%Y%m%d_%H%M%S)"
sudo cp -r /etc/nginx/sites-available $BACKUP_DIR/
sudo cp -r /etc/nginx/sites-enabled $BACKUP_DIR/
sudo cp /etc/caddy/Caddyfile $BACKUP_DIR/ 2>/dev/null || true
sudo cp -r /etc/caddy/233boy $BACKUP_DIR/ 2>/dev/null || true
```

#### 步骤3：获取VPN配置信息
```bash
# 获取所有VPN配置详情并记录
for config in $(find /etc -name "*dava.cf*.json" 2>/dev/null); do
    echo "=== $(basename $config) ==="
    sudo sb i "$(basename $config)" 2>/dev/null || echo "Failed to read $config"
    echo ""
done

# 获取sing-box监听端口
sudo netstat -tlnp | grep sing-box | awk '{print $4}' | cut -d':' -f2 | sort -u
```

#### 步骤4：停用caddy服务
```bash
# 停用caddy避免冲突
sudo systemctl stop caddy 2>/dev/null || true
sudo systemctl disable caddy 2>/dev/null || true

# 清理可能存在的VPN域名配置
sudo rm -f /etc/nginx/sites-enabled/*dava.cf* 2>/dev/null || true
```

#### 步骤5：为每个VPN域名创建nginx配置
```bash
# 基于获取的信息，为每个域名创建配置文件
# 模板格式：
sudo tee /etc/nginx/sites-available/DOMAIN_NAME << 'EOF'
server {
    listen 80;
    server_name DOMAIN_NAME;
    
    location UUID_PATH {
        proxy_pass http://127.0.0.1:LOCAL_PORT;
        proxy_redirect off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 5d;
    }
    
    location / {
        return 404;
    }
}
EOF

# 启用配置
sudo ln -sf /etc/nginx/sites-available/DOMAIN_NAME /etc/nginx/sites-enabled/DOMAIN_NAME
```

#### 步骤6：获取SSL证书
```bash
# 确保certbot已安装
sudo apt update && sudo apt install -y certbot python3-certbot-nginx

# 为每个域名获取SSL证书
sudo certbot --nginx -d DOMAIN_NAME --non-interactive --agree-tos --email <EMAIL>
```

#### 步骤7：修正WebSocket配置
```bash
# 修正nginx配置以支持WebSocket验证
sudo tee /etc/nginx/sites-available/DOMAIN_NAME << 'EOF'
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name DOMAIN_NAME;

    ssl_certificate /etc/letsencrypt/live/DOMAIN_NAME/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/DOMAIN_NAME/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
    
    location UUID_PATH {
        if ($http_upgrade != "websocket") {
            return 404;
        }
        proxy_pass http://127.0.0.1:LOCAL_PORT;
        proxy_redirect off;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_read_timeout 5d;
    }
}

server {
    listen 80;
    server_name DOMAIN_NAME;
    return 301 https://$host$request_uri;
}
EOF
```

#### 步骤8：验证和重新加载
```bash
# 测试nginx配置
sudo nginx -t

# 重新加载nginx
sudo systemctl reload nginx

# 验证服务状态
sudo systemctl status nginx
sudo netstat -tlnp | grep ':443'
```

#### 步骤9：生成客户端配置
```bash
# 创建客户端配置文件
sudo tee /tmp/vpn_client_config.yaml << 'EOF'
proxies:
  - {
    name: 🚀 DOMAIN_NAME,
    server: DOMAIN_NAME,
    port: 443,
    type: vmess,
    uuid: UUID_VALUE,
    alterId: 0,
    cipher: auto,
    tls: true,
    network: ws,
    ws-opts: {
      path: UUID_PATH,
      headers: {
        Host: DOMAIN_NAME
      }
    },
    sni: DOMAIN_NAME
  }
EOF

# 输出配置文件位置
echo "客户端配置已生成: /tmp/vpn_client_config.yaml"
```

## 🔧 具体服务器信息

### 服务器1 (Dava)
- **连接**: `ssh -i ~/.ssh/Dava.key ubuntu@**************`
- **配置文件**: VMess-WS-TLS-v2ray1.dava.cf.json, VMess-WS-TLS-vpn1.dava.cf.json
- **预期端口**: 36906, 6081

### 服务器2 (Eva)  
- **连接**: `ssh -i ~/.ssh/eva.key ubuntu@**************`
- **配置文件**: VMess-WS-TLS-v2ray2.dava.cf.json, VMess-WS-TLS-vpn2.dava.cf.json
- **预期端口**: 12040, 40576

## 📋 已知配置映射

| 域名 | UUID | 路径 | 本地端口 |
|------|------|------|----------|
| v2ray1.dava.cf | 7e2fc85c-856c-4fbf-b0cf-441b1ce23a85 | /7e2fc85c-856c-4fbf-b0cf-441b1ce23a85 | 36906 |
| vpn1.dava.cf | cabf9de6-edd2-4a21-a4fb-d26927adb1f4 | /cabf9de6-edd2-4a21-a4fb-d26927adb1f4 | 6081 |
| v2ray2.dava.cf | df750357-4558-428a-af6c-363ade00257a | /df750357-4558-428a-af6c-363ade00257a | 12040 |
| vpn2.dava.cf | 927719a9-ea68-4dc3-b576-6d79b79f4c0f | /927719a9-ea68-4dc3-b576-6d79b79f4c0f | 40576 |

## ⚠️ AI执行注意事项

1. **安全第一**: 始终先备份现有配置
2. **验证确认**: 每个步骤完成后验证结果
3. **错误处理**: 如果某步失败，记录错误信息并停止
4. **信息收集**: 详细记录所有输出，便于用户了解过程
5. **最终确认**: 完成后提供完整的验证结果和客户端配置

## 🎯 成功标准

修复完成后应满足：
- ✅ nginx正常运行在443端口
- ✅ caddy服务已停用
- ✅ SSL证书获取成功
- ✅ WebSocket代理配置正确
- ✅ 生成正确的客户端配置文件

## 🔄 失败恢复

如果任何步骤失败：
```bash
# 恢复备份
sudo cp -r $BACKUP_DIR/sites-available/* /etc/nginx/sites-available/
sudo cp -r $BACKUP_DIR/sites-enabled/* /etc/nginx/sites-enabled/
sudo systemctl reload nginx

# 如需要，重启caddy
sudo systemctl enable caddy
sudo systemctl start caddy
``` 